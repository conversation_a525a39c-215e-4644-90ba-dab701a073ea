prompt
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
创建React项目并安装Semi-design组件库
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始Phase 1: 数据收集与分析。我需要先读取现有的MR分析报告和单元测试评估结果，全面了解MR #278的技术细节。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
使用浏览器工具登录火山引擎合作伙伴控制台，并开始探索订单查询模块的功能和界面
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已完成！成功创建了专业且有趣的飞书文档报告，包含完整的代码质量分析、数据可视化图表、幽默点评和详细的优化建议。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已完成，所有功能需求都已实现，代码质量良好，文档完整
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark_creation.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
李逸嘉同学完成了飞书云文档的项目： `https://bytedance.larkoffice.com/wiki/FMHSwTn83iayRdk8tqXcKxlWnkd`，其主要的工作都记录在了文档中。请你写一篇QM平台的功能介绍文档，包含：一句话介绍项目、核心功能、共建指南等
请按照同样的格式，为项目二：`https://bytedance.larkoffice.com/wiki/IB65wmbWNiJTsQk5XXWcyhUWn9g` 项目三：
- 需求背景
     - 期望打通多个流程平台和测试数据构造平台，让这些数据在QM平台集中流转，提高测试效率和流转成本，同时解决离岸权限部分问题，使得某些测试可以让离岸同学完成闭环
     - 举个例子，番茄QA希望测试一本书籍，中间某一个章节是没有过审的，这个测试数据的构造涉及到多个平台，需要有一个快捷的方式来完成相关数据的构造和流转
- 项目目标
     - 打通bits、meego、libra、settings、byteconf、codebase等多个平台的，将相关数据进行封装和流转，提供简单的操作界面完成对应的事情
     - 比如能够依照提测文档【智能】配置好测试环境
     - 需要做好权限控制
- 阶段性目标
     - 考虑这份提测文档：【提测文档】 首页tab-多顶tab-刷新机制优化（主动刷新功能新增+超时自动刷新补齐），测试环境的构造涉及实验和settings，期望阶段性目标能实现【libra did加白】【settings did加白】的原子，并构建一条【用户提供did】->【给libra加白】->【给settings加白】的pipeline，原子需要提供预埋参数的能力，为日后引入LLM智能生成pipeline做准备
- 该功能与Mock工厂的差异
     - mock 工厂负责工具基础能力沉淀偏
单个工具，小说 QM 偏流程化，将这些工具串起来，流转调用。
- 人力安排（3人）

整理并撰写了功能介绍文档

[Expected Artifacts]
Lark/Feishu Document: 用于撰写和展示整理后的功能介绍文档，内容将包括项目背景、目标、阶段性规划等。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark_creation.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
你是一个具有技术背景的客服专家，现在你会拿到一批客服会话数据，我需要你帮我筛选出其中的技术类问题反馈。

包括但不限于：页面白屏、页面空白、点击没反应、点不动、页面加载太慢等可能是技术 Bug 的反馈。

对于确定是上述问题的进线，可以做一个归类汇总，同时在分类下列出明细，明细包含 VOC 详情、以及能反应该问题的关键信息。请将其中交互功能问题、页面显示问题涉及到的会话明细以表格形式列出来，需要包含会话链接（也就是 VOC 详情），以及能反应是该类问题的会话内容。

[Expected Artifacts]
Lark/Feishu Document: 用于创建包含会话明细表格的结构化文档，可以很好地展示会话链接、问题类型、会话内容等信息，并支持表格格式的清晰展示
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""files.dynamic_13"">
<title>read data files</title>
<use_this_knowledge_when>
when read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.
</use_this_knowledge_when>
</item>

<item id=""lark_creation.dynamic_35"">
<title>generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 请基于之前创建的《产品全量用户手册质检规则标准》文档，将评估维度和检查规则汇总成一个规则清单总表。

**任务要求：**

1. **提取和分析现有规则**：
   - 从工作区中的`产品全量用户手册质检规则标准_v3.lark.md`文件中提取所有评估维度和检查规则
   - 理解六个核心维度的具体要求和评分标准
   - 分析100分制评分体系下的违规后果

2. **按指定格式整理成表格**：
   表格必须包含以下表头：
   ```
   | 规则大类 | 规则细则/具体要求 | 限制要求/标准 | 违规后果/处罚措施 | 备注说明 |
   ```

3. **内容转换规则**：
   - **规则大类**：将六个评估维度作为规则大类（任务导向性、信息架构、技术准确性与完整性、可读性与可理解性、视觉设计与多媒体支持、可维护性与可扩展性）
   - **规则细则/具体要求**：将每个维度下的具体检查要点和评估角度转换为明确的规则细则
   - **限制要求/标准**：明确每个规则的具体标准和阈值要求
   - **违规后果/处罚措施**：基于100分制评分体系，说明不达标的扣分情况或影响
   - **备注说明**：添加必要的解释说明或特殊情况处理

4. **特殊要求**：
   - 保留合规性要求（安全性、图片合规）的一票否决机制
   - 确保表格内容完整、逻辑清晰
   - 创建飞书文档作为最终交付物

请确保最终的规则清单总表既保持了原有质检标准的完整性，又以更直观的表格形式呈现，便于实际使用和执行。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""files.dynamic_13"">
<title>read data files</title>
<use_this_knowledge_when>
when read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.
</use_this_knowledge_when>
</item>

<item id=""lark.tool_lark_doc_generation"">
<title>Feishu/Lark Doc Generation</title>
<use_this_knowledge_when>
when generating Feishu/Lark documents

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 基于前面对 go-union-i18n 代码仓库的深入分析结果，现在需要完成以下任务：

1. **重构方案设计**：
   - 基于分析中发现的耦合点、复杂度问题和改进方向，设计具体的重构优化方案
   - 重点关注降低组件间耦合度、简化复杂决策流程、提升代码可测试性
   - 设计更清晰的接口抽象和模块划分

2. **代码实现**：
   - 在新的目录结构中实现重构后的代码
   - 保持原有功能的完整性，同时优化架构设计
   - 提供清晰的接口定义和实现分离
   - 增强代码的可测试性和可维护性

3. **生成Lark文档**：
   - 创建一个完整的Lark文档，包含：
     - 原有系统的调用链路分析和实现梳理
     - 发现的问题和改进点
     - 详细的重构优化方案设计
     - 重构前后的对比分析
     - 重构后的代码架构说明
   - 使用适当的图表和代码示例来增强文档的可读性

请确保重构方案既保持了原有系统的核心功能，又显著提升了代码的质量和可维护性。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""data_analyze.dynamic_18_aeolus"">
<title>aeolus tips</title>
<use_this_knowledge_when>
When the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.planner_19_template"">
<title>Planning By Lark Template with Default SOP</title>
<use_this_knowledge_when>
use it when user mentioned '参考飞书模板' or '基于飞书模板' or '完成这个模板' or other such words.

</use_this_knowledge_when>
</item>

<item id=""data_analyze.planner_3"">
<title>llm tasks: data classification, labeling, filter, or other natural language processing tasks tips</title>
<use_this_knowledge_when>
perform nlp related tasks.
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>

<item id=""developer.planner_15"">
<title>MR Review Guidelines</title>
<use_this_knowledge_when>
only if the user task involves MR(Merge Request) code review
</use_this_knowledge_when>
</item>

<item id=""SlardarApp.alog_analysis_iOS"">
<title>iOS alog 智能分析</title>
<use_this_knowledge_when>
当用户的输入中包含alog日志分析、结合alog分析反馈问题等相关提示时
</use_this_knowledge_when>
</item>

<item id=""SlardarWeb.slardar_web"">
<title>Slardar Bug/Standard Process for Frontend Error Fixing or Performance Analyzing</title>
<use_this_knowledge_when>
When the user mentions the Slardar web scenario.
</use_this_knowledge_when>
</item>

<item id=""SlardarOs.slardar_os"">
<title>SlardarOS排障场景</title>
<use_this_knowledge_when>
When the user mentions the Slardar OS scenario.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
我是飞书妙记媒体侧服务端的校招生员工，目前刚进组2个月了，明天就要跟leader lunch one one了，我没有one one的经验，应该注意哪些事项？下面是我两个月工作的阶段性总结：

两个月工作阶段性总结：
- 从文件管理模块出发切入了统一拉流项目，梳理了StateLog和RTCLog的写入场景并完成迁移；
- 测试录制全量case，分配到布局计算相关case，测试并修复了10+个P0级Bug；
- 根据日志排查，消除重构前后代码Layout diff 和 Rtc diff，取得一定成效,附件是效果图；
- 与新亮、步威一起开发音频容灾管理模块，其中个人负责开发了音频wav和ref文件上传TOS，以及根据streamkey列出 Tos 上对应目录所有文件功能；
- 统一拉流项目，部分埋点开发及对应的Grafana面板、Argos告警配置；
- 音频双声道反向调研，初步调研出解决方案，9月初（目前）进行方案验证。你说的很好，现在，从一个给leader正面印象， 有利于我之后试用期转正及绩效评估（最好能达到M+）的角度出发，结合目前的工作产出情况，我希望你能够给我模拟一个我与我leader交流的场景，让我能随和而不露声色（不显得刻意而急功近利）地达到上述目的。我的leader实际上这两个月并没有直接指导我，我基本上都是由mentor带领的，所有他并不是很清楚我具体做了什么。我希望我与leader之间的对话是长对话，轻松惬意而不失深度。


[Expected Artifacts]
Lark/Feishu文档: 提供与leader深度对话的完整指南，包括对话策略、话题准备、沟通技巧和具体的对话框架建议
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""search.dynamic_32"">
<title>Global Planning and Batch Search Strategy</title>
<use_this_knowledge_when>
task involves search or research
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
Proceed with Phase 2: Content Generation & Visualization. I will start by drafting the content for the report and creating the Mermaid diagram.
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 请继续完成 module_269 的埋点事件分析任务。你已经成功找到了3个 `collector.collect` 调用，现在需要完成详细的事件分析并生成最终的 JSON 文件。

**已找到的事件位置：**
- `src/uba-ui/Period/creators/createPeriodDropdownButton.ts`
- `src/uba-ui/Period/creators/createPeriodDashboardDropdownButton.ts`  
- `src/uba-ui/Period/components/PeriodVs/index.tsx`

**需要完成的任务：**

1. **详细分析每个事件调用**
   - 提取准确的事件名称（解析变量引用为静态字符串）
   - 获取完整的原始调用代码
   - 确定准确的行号和文件路径
   - 分析调用上下文和触发条件
   - 对事件进行分类（用户交互、生命周期、业务逻辑、定时/条件触发）

2. **变量解析**
   - 对于变量引用的事件名称，追踪到常量定义
   - 如果无法解析，标记为 unresolved_events

3. **生成完整的 JSON 文件**
   - 严格按照 LABEL_gen2.md 规范生成 `step3_events_analysis_module_269.json`
   - 包含所有必需字段：module_name, events, event_constants, unresolved_events, summary
   - 确保 JSON 格式完全符合规范

**输出要求：**
- 必须生成完整的 `step3_events_analysis_module_269.json` 文件
- 每个事件都要包含完整的分析信息
- 提供清晰的总结说明

请仔细分析每个找到的事件调用，确保分析的准确性和完整性。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已全部完成！成功创建了包含所有156个权限操作码的完整技术参考文档，并生成了飞书文档供用户查阅。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
角色
你是一个专注于生活服务电商领域平台治理的资深产品经理专家，拥有丰富的业务理解力和实战经验，专门处理PRD（产品需求文档）的完善和优化工作，能够将复杂的业务需求转化为清晰、可执行的产品文档。
工作流程
输入获取：接收用户提供的PRD草稿或需求描述，确保成功理解业务背景、目标用户、核心功能需求，并准备进行分析和完善。


分析/处理逻辑：
业务维度：深入分析生活服务电商的业务模式，治理团队的核心痛点和机会点。
功能维度：梳理功能需求的完整性、逻辑性和可行性，确保功能设计符合业务目标和用户需求。
技术维度：评估技术实现的复杂度和可行性，提供清晰的可落地方案及交互说明
运营维度：考虑运营使用便捷、产品能力好理解等运营层面的需求。
管理维度：确保文档结构清晰、表达准确，便于不同角色理解和执行。
结果输出：根据分析结果，输出结构完整、逻辑清晰的PRD文档，必要时补充产品逻辑图，确保运营和老板能够理解，研发能够执行。飞书文档链接私密，仅我可看，我为管理员。


规则/分析维度
规则1：结构完整性
PRD必须包含：需求背景（业务痛点、解题思路、业务目标）、产品方案简述（用户画像及对应的核心产品功能，用户动线、产品逻辑及架构）、需求详情（每个功能模块的具体要求，越详细越好）、运营策略或机制，项目计划等核心模块
每个模块内容详实，逻辑清晰，避免遗漏关键信息
规则2：表达清晰性
使用标准化的产品文档格式，但信息不要冗余，一级目录与我的原始prd结构大体保持一致即可
功能描述具体、明确，避免模糊表达
关键流程配备流程图或逻辑图
重要概念提供明确定义
规则3：可执行性
功能需求具备明确的字段说明、逻辑说明、交互说明
运营策略具备可操作的执行步骤
项目计划包含明确的时间节点和责任人
规则4：业务适配性
深度结合生活服务电商的行业特点
充分考虑平台治理的特殊需求（如商家管理、服务质量控制、用户体验优化等）
方案设计符合监管要求和行业最佳实践
规则5：沟通有效性
面向运营和管理层：重点突出业务痛点、业务价值、解题思路，需要清晰简洁好理解。
面向研发团队：提供详细的功能描述、交互描述，不要过度简化我的prd内容。
使用图表、流程图等可视化工具辅助理解

[Expected Artifacts]
Feishu/Lark文档: 用于生成结构完整、逻辑清晰的PRD产品需求文档，包含需求背景、产品方案、功能详情、运营策略等完整模块，面向运营、管理层和研发团队
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 你之前的分析遇到了搜索工具的问题。现在请重新分析 module_296，采用以下方法：

1. **明确任务目标**：你需要创建 step3_events_analysis_module_296.json 文件，而不是寻找已存在的文件

2. **手动文件检查**：由于 grep_search 工具可能不可靠，请手动检查 module_296 中的关键文件：
   - 从 step1_modules_split.json 中已知 module_296 包含30个文件，都在 src/uba-utils/meta/hooks/ 目录下
   - 这些文件主要是 React hooks 工具函数
   - 手动打开几个代表性文件，查看是否包含：
     - Tea SDK 直接调用（tea.event, Tea.event 等）
     - 封装函数调用（如 track(), logEvent(), collector.collect() 等）

3. **基于实际情况生成 JSON**：
   - 如果确实没有发现埋点事件调用，生成一个包含空事件列表的 JSON 文件
   - JSON 格式必须严格按照 LABEL_gen2.md 规范：
     ```json
     {
       ""module_name"": ""module_296"",
       ""events"": [],
       ""event_constants"": {},
       ""unresolved_events"": [],
       ""summary"": {
         ""total_events"": 0,
         ""resolved_events"": 0,
         ""unresolved_events"": 0,
         ""files_with_events"": 0,
         ""total_files_analyzed"": 30
       }
     }
     ```

4. **保存文件**：将生成的 JSON 保存为 step3_events_analysis_module_296.json

请立即开始执行，不要再依赖可能失效的搜索工具，直接手动检查文件内容。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""search.dynamic_32"">
<title>Global Planning and Batch Search Strategy</title>
<use_this_knowledge_when>
task involves search or research
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 基于用户的要求，需要完善豆包教育未来半年规划提纲框架文档的现状分析部分：

1. **读取源文档内容**：
   - 仔细阅读workspace中的""豆包教育_业务OnePage（WIP）.lark.md""文档
   - 识别其中的关键技术现状信息和架构图

2. **内容摘抄和整合**：
   - 从源文档中摘抄适合放在现状分析部分的内容
   - 特别关注架构图，确保完整复制到目标文档
   - 选择与豆包教育业务相关的技术现状描述

3. **更新提纲框架文档**：
   - 在现有的提纲框架文档基础上，完善""2. 现状分析""部分
   - 将摘抄的内容和架构图合理地整合到相应的子章节中
   - 保持原有的编号体系和提纲结构

4. **输出要求**：
   - 更新现有的Feishu/Lark提纲框架文档
   - 确保架构图能正确显示
   - 保持文档的整体格式和风格一致性

请基于现有的提纲框架文档进行内容补充，重点是现状分析部分的完善。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>

## Objective:
    将所有生成的 Markdown 文档和图片，整合成一份结构清晰的飞书（Lark）云文档，作为最终交付物。

## Actions(You should adapt the actions to the current task):
    1.  使用 `lark_creation` 工具，以上述聚合文件为内容，创建一个标题为“Aime 项目后端完整技术方案”的飞书云文档:
      ** 按照逻辑顺序（需求分析 -> 架构 -> ERD -> ... -> 改造计划），将所有 `.md` 文件的内容聚合到该临时文件中，并调整标题层级
      ** 确保图片引用路径正确
    2.  输出新创建的飞书云文档链接。

## Deliverable:
- [最终技术方案飞书文档] (Lark document URL)

</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""files.dynamic_13"">
<title>read data files</title>
<use_this_knowledge_when>
when read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.
</use_this_knowledge_when>
</item>

<item id=""lark.tool_lark_doc_generation"">
<title>Feishu/Lark Doc Generation</title>
<use_this_knowledge_when>
when generating Feishu/Lark documents

</use_this_knowledge_when>
</item>

<item id=""lark.tool_lark_progress_example_newbie"">
<title>progress example - Lark</title>
<use_this_knowledge_when>
when the task needs designing and generating lark reports or documents, be aware that this is only for new lark document creation.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 作为一名资深的技术架构师，请仔细审查并分析提供的技术方案文档 `Rax_（USB）连接优化方案.lark.md`。

你的任务是：
1.  **阅读并理解**：完整阅读 `Rax_（USB）连接优化方案.lark.md` 的内容，理解其核心目标、现有问题和提出的解决方案。
2.  **批判性分析**：从技术架构、系统设计、可扩展性、安全性、可维护性和用户体验等多个维度，对方案进行深入分析。
3.  **撰写分析报告**：基于你的分析，创建一个新的飞书文档。报告必须包含以下三个核心部分，并进行详细阐述：
    *   **存在的问题**：明确指出当前方案中可能存在的设计缺陷、技术风险或不合理的假设。
    *   **遗漏的点**：补充方案中未能覆盖到的重要方面，例如：异常处理、监控告警、向后兼容性、成本效益分析或未来的可扩展性等。
    *   **可以优化的点**：提出具体的、可行的优化建议，以提升方案的性能、稳定性、开发效率或降低成本。

最终产物应为一份结构清晰、论点明确、建议具体的专业技术评审报告。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""files.dynamic_13"">
<title>read data files</title>
<use_this_knowledge_when>
when read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>

## Objective:
    解析 `https://code.byted.org/ti/dsa_sched_strategy/merge_requests/97`，使用 `codebase` 工具获取 MR 的详细信息，特别是源分支和目标分支的 Commit ID。然后，使用 Git 命令将本地仓库设置为待审查的代码状态。

## Actions(You should adapt the actions to the current task):
    1.  **解析 MR URL**：从 `https://code.byted.org/ti/dsa_sched_strategy/merge_requests/97` 中提取仓库 ID 和 MR 编号。
    2.  **获取 MR 详情**：调用 `codebase.get_merge_request` 工具，传入仓库 ID 和 MR 编号，获取 MR 的元数据。
    3.  **提取并保存 Commit ID**：从返回结果中解析出 `source_commit_id` 和 `base_commit_id`。将这两个 ID 保存到一个临时工作文件（如 `commit_ids.json`）中，以便后续步骤使用。
    4.  **准备本地仓库**：
        -   进入本地代码仓库目录。
        -   执行 `git fetch` 拉取最新的远程对象。
        -   执行 `git checkout [source_commit_id]`，将工作区切换到 MR 源分支的最新代码状态。

## Deliverable:
- [本地代码仓库] (已切换到待审查的 commit)
- [Commit ID 文件] (e.g., commit_ids.json)

</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_4_html_citation"">
<title>HTML Citation and Reference Management</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 **🚨 MANDATORY FIRST**: 你的第一个操作必须是使用lark_download工具下载用户提供的参考文档。在完成这个操作之前，不要进行任何其他操作。

用户发现了一个重要的数据问题：当前图表中显示站内CTR互动类比IM类高，这与用户的认知不符。需要核实数据并修正：

## 核心任务要求

### 1. 数据核实和分析
- **下载参考文档**：https://bytedance.larkoffice.com/docx/MPBHddrFeoNX5lxNNitco1x5niy
- **仔细分析文档中的CTR数据**：
  - 站内推送各类型的真实CTR数据
  - 站外推送各类型的真实CTR数据
  - 特别关注IM类和互动类的CTR对比
- **数据交叉验证**：与之前收集的其他文档数据进行交叉验证

### 2. 问题识别和修正
- **识别数据差异**：
  - 当前图表显示的数据vs参考文档的数据
  - 找出具体的数据错误点
  - 分析可能的数据来源混淆原因
- **数据修正**：
  - 基于参考文档的准确数据
  - 确保IM类和互动类CTR的正确关系
  - 验证其他类型（发文、推人）的数据准确性

### 3. 图表重新生成
- **重新生成站内推送图表**：
  - 使用准确的CTR数据
  - 确保IM类CTR数据的准确性
  - 保持图表的清晰标注和内部化风格
- **重新生成站外推送图表**：
  - 基于参考文档的准确数据
  - 确保各类型推送CTR的正确展示
- **数据一致性检查**：确保新图表数据逻辑合理

### 4. 文档更新
- **更新飞书文档**：
  - 替换错误的图表
  - 修正相关的数据引用和说明
  - 确保文档中所有CTR数据的一致性
- **保持文档结构**：
  - 不改变整体结构和逻辑
  - 仅修正数据错误部分
  - 保持其他优化成果

### 5. 数据验证和说明
- **数据来源说明**：
  - 在文档中标注数据来源
  - 说明修正的原因和依据
- **逻辑一致性检查**：
  - 确保修正后的数据支持原有的分析结论
  - 如有必要，调整相关的分析表述

### 6. 质量保证
- **准确性验证**：确保所有数据来源可靠
- **可读性保持**：修正后的图表仍然清晰易读
- **专业性维护**：保持文档的专业水准

请优先处理这个数据准确性问题，确保图表和文档中的CTR数据完全准确。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
首先查看现有项目结构，然后创建 metrics 包的目录结构和核心文件
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
基于已有的关键词列表和需求分析，制定针对4个重点领域的全面搜索策略，准备执行批量技术文档搜索
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 解耦重构的主要工作已经完成，现在需要完成最后的验证和提交步骤：

**主要任务：**

1. **Git状态检查和代码提交**
   - 检查当前git状态，查看所有修改的文件
   - 创建合适的分支（如果需要）
   - 提交解耦重构的代码变更，使用清晰的commit message
   - 准备创建Merge Request（如果用户需要）

2. **最终验证报告**
   - 总结本次解耦重构的完成情况
   - 列出成功处理的文件和依赖关系
   - 说明遗留问题：AgeGraduationUtil.kt需要人工处理
   - 提供后续建议

3. **问题汇报**
   - 明确说明需要用户人工处理的具体问题
   - 解释为什么某些问题需要人工介入
   - 提供解决建议

**重要说明：**
- 由于构建环境缺少ANDROID_HOME配置，编译验证无法完成，但代码修改符合规范
- 需要向用户说明AgeGraduationUtil.kt文件的logger定义问题
- 准备完整的工作总结，包括已完成和待处理的项目

请完成这些最终步骤，并准备向用户汇报整个解耦任务的执行结果。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""data_analyze.2"">
<title>LLM Guide for Data Analysis and Classification</title>
<use_this_knowledge_when>
Process text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_18_aeolus"">
<title>aeolus tips</title>
<use_this_knowledge_when>
When the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_1"">
<title>data analysis working principles</title>
<use_this_knowledge_when>
when processing or analyzing data (including git history, maps, tables, etc.)
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_3"">
<title>table data processing principles</title>
<use_this_knowledge_when>
when need process/filter/generate excel/csv data files
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 You need to use the GEC搜索工具v2_18 (automatic evaluation tool) to evaluate the first 90 queries from the provided evaluation link. The task involves:

1. **Data Acquisition**: Access the evaluation link `https://search-og-us.tiktok-row.net/garr/fetch_review/shot_history?eid=585432036672587&name=%E3%80%90US%20relevance%E3%80%91union_smr%20&app_id=265652` and extract the first 90 queries.

2. **Automatic Evaluation**: Use the GEC搜索工具v2_18 to perform relevance evaluation on the top 6 results for each query. The evaluation should focus on the relevance dimension as specified.

3. **Process Flow**: Follow the three-step process of the GEC tool:
   - Use `get_tids_from_url()` to generate the tid list for evaluation
   - Use `sbs_tids()` to evaluate each tid and get individual results
   - Use `generate_relevance_conclusion()` to aggregate all results and generate conclusions

4. **Data Output**: Save all processed query data and evaluation results in structured files for later report generation.

The evaluation should cover relevance assessment for the top 6 search results of each of the first 90 queries from the provided link. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""data_analyze.2"">
<title>LLM Guide for Data Analysis and Classification</title>
<use_this_knowledge_when>
Process text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_18_aeolus"">
<title>aeolus tips</title>
<use_this_knowledge_when>
When the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_1"">
<title>data analysis working principles</title>
<use_this_knowledge_when>
when processing or analyzing data (including git history, maps, tables, etc.)
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_25_2"">
<title>guide for preparing content for lark templates reports</title>
<use_this_knowledge_when>
use it whenever/wherever preparing content for a lark template or markdown report, analyzing data or generating visualization part for reports, even if the current task is just creating intermediate artifacts or doing analysis that will later be integrated into a final document.

</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_3"">
<title>table data processing principles</title>
<use_this_knowledge_when>
when need process/filter/generate excel/csv data files
</use_this_knowledge_when>
</item>

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>

<item id=""search.dynamic_26"">
<title>search must have knowledge</title>
<use_this_knowledge_when>
You MUST use this knowledge if the following steps need use search tool or research tool.
</use_this_knowledge_when>
</item>

<item id=""search.dynamic_32"">
<title>Global Planning and Batch Search Strategy</title>
<use_this_knowledge_when>
task involves search or research
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 请帮我处理这个Bilibili视频链接：https://www.bilibili.com/video/BV1Lm4y1273Q/?spm_id_from=333.1387.upload.video_card.click

任务要求：
1. 访问该Bilibili视频，获取视频的基本信息（标题、时长、描述、UP主等）
2. 提取视频中的音频内容和字幕信息（如果有的话）
3. 将视频内容转录成文本
4. 整理成结构化的文档，包括：
   - 视频基本信息
   - 内容要点总结
   - 详细内容整理
   - 关键概念或术语解释（如适用）
5. 最终生成飞书文档格式，便于阅读和分享

请确保内容准确，结构清晰，方便读者理解。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
  .withColumn('is_duplicate', F.coalesce(F.col('is_duplicate'), 0))   File ""/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/functions.py"", line 925, in coalesce   File ""/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py"", line 61, in _to_seq   File ""/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py"", line 61, in <listcomp>   File ""/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py"", line 49, in _to_java_column TypeError: Invalid argument, not a string or column: 0 of type <class 'int'>. For column literals, use 'lit', 'array', 'struct' or 'create_map' function.

[Expected Artifacts]
Direct Answer: The user has provided a traceback and needs an explanation of the error and a solution. A direct text answer is the most appropriate and efficient format for this kind of debugging help.
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
详细介绍一下VIPER架构


[Expected Artifacts]
Lark/Feishu Document: 用于撰写关于VIPER架构的详细介绍报告，包括其核心组件、数据流、优缺点等内容。
Diagrams: 用于创建一个可视化图表，清晰地展示VIPER架构中各个组件（View, Interactor, Presenter, Entity, Router）之间的关系和交互流程。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始Phase 1的最后一步：检查目标文件是否存在并分析其内容结构，确保我们有正确的源文件来创建Lark文档。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""planner.planner_18"">
<title>Planning Example</title>
<use_this_knowledge_when>
always
</use_this_knowledge_when>
</item>

<item id=""planner.planner_1"">
<title>rich format file convert tips</title>
<use_this_knowledge_when>
when user provide rich format files(such as pdf, microsoft office files, etc.) and need to understand. or need to convert file format.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_21"">
<title>Text-to-Speech (TTS) capability knowledge</title>
<use_this_knowledge_when>
You MUST use this knowledge when user requests text-to-speech conversion.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_23"">
<title>Word Document Processing Capability</title>
<use_this_knowledge_when>
When user asks to read, extract, or convert Word documents
</use_this_knowledge_when>
</item>

<item id=""planner.planner_24"">
<title>Batch Processing Capability for Mewtwo</title>
<use_this_knowledge_when>
When tasks involve repetitive operations that can benefit from batch processing - multiple similar operations on different inputs.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_25"">
<title>Data Visualization</title>
<use_this_knowledge_when>
the user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_12"">
<title>Plan guide for tasks operations on specific website</title>
<use_this_knowledge_when>
when user query contains task that is related to downloading data from specific website, exporting data, etc.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
给我详细讲讲Spark的概念、原理、使用场景、最佳实践，以及基本的操作讲解。多来几张图讲解
细说用户的sql是怎么转成 RDD、DataFrame并构造DAG的细说 Spark 的 DataFrame、DataSet、RDD，在原理层面和使用层面细说 Spark 的 DataFrame、DataSet、RDD，在原理层面和使用层面，以及他们和真实物理存储的映射关系

[Expected Artifacts]
Lark/Feishu Document: 用于撰写关于 Spark DataFrame、DataSet 和 RDD 的详细分析报告，包含原理、使用方法和存储映射等结构化内容。
Diagrams: 用于绘制图表，以可视化方式解释 RDD、DataFrame、DataSet 的内部原理、相互关系以及与物理存储的映射关系，并嵌入到文档中。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
首先阅读项目的 README.md 文件，了解项目的基本概述、主要特性和使用指南
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已完成，成功生成了包含变更概述、实现特点、代码质量评估、潜在影响分析和改进建议的结构化代码分析报告
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""files.dynamic_13"">
<title>read data files</title>
<use_this_knowledge_when>
when read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.
</use_this_knowledge_when>
</item>

<item id=""lark.tool_lark_doc_generation"">
<title>Feishu/Lark Doc Generation</title>
<use_this_knowledge_when>
when generating Feishu/Lark documents

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 请分析用户提供的JSON配置代码，这看起来是一个隐私保护相关的服务配置。需要你：

1. **结构解析**: 分析整个JSON配置的层次结构和组织方式
2. **字段解读**: 详细解释各个字段的含义和作用，包括但不限于：
   - version_id, psm等基础配置
   - method_configs中的认证阶段、策略配置
   - policy_config中的认证点、实体、策略详情
   - attributes中的属性定义和类型
   - action_config中的操作配置
   - traffic_control中的流量控制
   - log_config中的日志配置
3. **业务逻辑分析**: 解释这个配置实现的业务功能和隐私保护机制
4. **生成报告**: 将分析结果整理成一份结构化的Feishu/Lark文档

配置代码：
```json
{
    ""version_id"": 33,
    ""psm"": ""aweme.privacy.minix_kitex_callee_demo"",
    ""method_configs"": [
        {
            ""method"": ""GetUserD"",
            ""auth_stage"": 2,
            ""policy_config"": [
                {
                    ""auth_point"": ""GetUserResponse"",
                    ""entity"": ""User"",
                    ""policies"": [
                        {
                            ""id"": 1001,
                            ""name"": ""user_id"",
                            ""policy_type"": 2,
                            ""attributes"": [
                                {
                                    ""id"": 1,
                                    ""name"": ""user_id"",
                                    ""attr_type"": 1,
                                    ""value_type"": 4,
                                    ""local_field"": {
                                        ""name"": ""user_id"",
                                        ""type"": 4,
                                        ""expr"": ""fe_user_id"",
                                        ""auth_point"": ""GetUserRequest""
                                    }
                                },
                                {
                                    ""id"": 2,
                                    ""name"": ""is_aweme"",
                                    ""attr_type"": 3,
                                    ""value_type"": 3,
                                    ""func"": ""privacy.minix_test_feature_bool""
                                }
                            ],
                            ""exprs"": [
                                ""is_aweme == true""
                            ],
                            ""action_config"": {
                                ""default_config"": [
                                    {
                                        ""action_type"": 4,
                                        ""operate_fields"": [
                                            ""GetUserResponse.User.ShortId""
                                        ]
                                    }
                                ]
                            },
                            ""traffic_control"": {
                                ""switch"": true,
                                ""sample_prop"": 10000,
                                ""block_prop"": 10000,
                                ""timeout"": 30
                            }
                        }
                    ]
                }
            ],
            ""log_config"": {
                ""default_log_config"": {
                    ""local"": {
                        ""prop"": 10000,
                        ""err_prop"": 10000,
                        ""level"": [
                            ""Info"",
                            ""Notice"",
                            ""Warn"",
                            ""Error"",
                            ""Fatal""
                        ]
                    },
                    ""remote"": {
                        ""prop"": 10000,
                        ""err_prop"": 10000
                    },
                    ""metric"": 10000
                }
            },
            ""enable_ticket"": true
        }
    ]
}
```

请生成一份详细的Feishu/Lark文档，帮助用户快速理解这段配置代码的含义和功能。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
分析现有的SubmissionRecordServiceTest.java来了解正确的包路径和导入模式，然后为SubmissionService.java和AppletController.java生成正确的单元测试
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>

<item id=""developer.planner_15"">
<title>MR Review Guidelines</title>
<use_this_knowledge_when>
only if the user task involves MR(Merge Request) code review
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
  原始提示词
目前我们有一个iOS的代码仓库，请协助我完成一份能让LLM高效理解并执行的markdown规则，规则的主要内容为：根据下述规则，生成符合规范的UI代码
输入内容会带有figma链接，输出内容是对应的UI代码，且需要符合下面的规范
- 自动布局相关，使用snapkit

[Expected Artifacts]
Markdown Document: 用于编写和展示给LLM的UI代码生成规则。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
首先阅读需求文档和效果图，理解具体的功能需求
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
继续深入分析权限设置面板中的具体组件，查找可能存在条件性 Hooks 调用、循环中的 Hooks、动态 Hooks 数量等违反 Hooks 规则的情况
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
继续分析其他核心文件的变更，包括统一验证管理器、风控管理器、支付流程控制器等关键模块，然后开始对照技术方案文档进行符合性审查
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 基于之前分析的TikTok Shop新商半托管项目内容，深入分析和阐述""平台批量化服务""的概念和实现模式。请创建一份详细的分析文档：

## 分析要求：

### 1. 平台批量化服务的定义和概念
- **核心概念**：什么是平台批量化服务
- **服务模式**：与传统一对一服务的区别
- **平台角色**：平台在批量化服务中的作用和价值
- **批量化特征**：如何实现规模化和标准化

### 2. 服务架构和实现方式
- **技术架构**：平台如何支撑批量化服务
- **业务流程**：从商家需求到服务交付的完整流程
- **资源配置**：如何高效配置和管理服务商资源
- **质量控制**：批量化服务的质量保障机制

### 3. 核心特点和优势
- **规模效应**：批量化带来的成本和效率优势
- **标准化服务**：统一的服务标准和交付质量
- **平台保障**：平台提供的信用背书和风险控制
- **资源优化**：服务商资源的最优配置和利用

### 4. 具体实现案例
基于TikTok Shop新商半托管项目，说明：
- **商家端体验**：商家如何享受批量化服务
- **服务商端运作**：服务商如何参与批量化服务
- **平台端管理**：平台如何协调和管理整个服务体系
- **效果和价值**：批量化服务带来的实际效果

### 5. 与传统服务模式的对比
- **传统模式痛点**：一对一服务的局限性
- **批量化优势**：平台批量化服务的改进和创新
- **价值创造**：为各方参与者创造的独特价值
- **市场意义**：对行业发展的推动作用

## 输出要求：

### 1. 内容深度
- 从商业模式、技术架构、运营管理等多维度分析
- 结合具体的项目案例进行说明
- 突出平台批量化服务的创新性和价值

### 2. 文档格式
- 创建专业的Word文档和Markdown文件
- 使用图表和流程图辅助说明（如需要）
- 结构清晰，逻辑严谨

### 3. 目标受众
- 面向对平台服务模式感兴趣的商业人士
- 适合用于项目介绍和商业分析
- 可作为平台服务模式的参考文档

请基于项目实际情况，深入分析平台批量化服务的核心要素和实现模式。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""developer.coder_2"">
<title>Code Exploring and Understanding</title>
<use_this_knowledge_when>
when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
</use_this_knowledge_when>
</item>

<item id=""developer.coder_5"">
<title>Frontend Development Workflow Without A Specified Repository</title>
<use_this_knowledge_when>
when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
</use_this_knowledge_when>
</item>

<item id=""developer.coder_6"">
<title>Fullstack App Development Workflow</title>
<use_this_knowledge_when>
when user requests to build a web app that needs a backend service
</use_this_knowledge_when>
</item>

<item id=""developer.coder_9"">
<title>Get Frontend Development Knowledge</title>
<use_this_knowledge_when>
When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.

</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
准备文件内容分析 - 为每个待审查文件提取具体代码变更，创建详细的代码变更分析，为后续的专业代码审查做好准备
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
## 角色设定
你是资深Go架构师，按Google SRE标准对代码进行严格审查。

## 任务目标
审查GitHub PR的代码变更：https://github.com/apache/answer/pull/1265

## 执行步骤

### 1. 获取代码示例
```bash
git clone https://github.com/apache/answer/
cd answer
git fetch origin pull/1265/head:pr-1265
git diff origin/main...pr-1265 > diff.txt
```

### 2. 审查维度
- **架构**: SOLID原则、设计模式、技术债务
- **质量**: 圈复杂度>10、重复代码、并发安全
- **性能**: 时间复杂度、内存泄漏、N+1查询
- **安全**: OWASP风险、输入验证、权限检查
- **维护**: 文档、日志、错误处理

### 3. 输出格式
```
文件: path/to/file.go @L行号

[🔴严重/🟡警告] 问题类型
问题描述 (CWE-XXX)
影响: 性能★★☆ 安全★★★ 维护★☆☆
修复建议:
```go
// 改进代码
```

## 要求
- 跳过测试文件和依赖注入代码
- 每个diff块独立分析
- 仅输出有问题的代码
- 提供具体修复方案
- 最终以文档格式输出审查报告
- 确认给出的CWE-编号是确实存在的，且意义正确

[Expected Artifacts]
Feishu/Lark文档: 用于呈现按Google SRE标准完成的PR代码审查报告，包含问题分级、CWE映射、修复示例及维护建议
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"