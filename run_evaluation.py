#!/usr/bin/env python3
"""
Runner script for model evaluation
"""

import sys
import argparse
from model_evaluation import ModelEvaluator
from test_api_connections import test_apis

def main():
    parser = argparse.ArgumentParser(description='Model Evaluation Tool')
    parser.add_argument('--test', action='store_true',
                       help='Test API connections only')
    parser.add_argument('--data-file', default='data_big.json',
                       help='Path to JSON data file (default: data.json)'),
    parser.add_argument('--mode', default='claude',
                       help='Ground truth mode (claude or gemini)'),

    args = parser.parse_args()

    if args.test:
        print("Running API connection tests...")
        test_apis()
    else:
        print("Running full model evaluation...")
        evaluator = ModelEvaluator()
        evaluator.run_evaluation(args.data_file, args.mode)
        print(f"Evaluation completed!")

if __name__ == "__main__":
    main()
