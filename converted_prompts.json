[{"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n创建React项目并安装Semi-design组件库\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n开始Phase 1: 数据收集与分析。我需要先读取现有的MR分析报告和单元测试评估结果，全面了解MR #278的技术细节。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n使用浏览器工具登录火山引擎合作伙伴控制台，并开始探索订单查询模块的功能和界面\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n任务已完成！成功创建了专业且有趣的飞书文档报告，包含完整的代码质量分析、数据可视化图表、幽默点评和详细的优化建议。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego tool principle</title>\n<use_this_knowledge_when>\nwhen working with meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n任务已完成，所有功能需求都已实现，代码质量良好，文档完整\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark_creation.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n李逸嘉同学完成了飞书云文档的项目： `https://bytedance.larkoffice.com/wiki/FMHSwTn83iayRdk8tqXcKxlWnkd`，其主要的工作都记录在了文档中。请你写一篇QM平台的功能介绍文档，包含：一句话介绍项目、核心功能、共建指南等\n请按照同样的格式，为项目二：`https://bytedance.larkoffice.com/wiki/IB65wmbWNiJTsQk5XXWcyhUWn9g` 项目三：\n- 需求背景\n     - 期望打通多个流程平台和测试数据构造平台，让这些数据在QM平台集中流转，提高测试效率和流转成本，同时解决离岸权限部分问题，使得某些测试可以让离岸同学完成闭环\n     - 举个例子，番茄QA希望测试一本书籍，中间某一个章节是没有过审的，这个测试数据的构造涉及到多个平台，需要有一个快捷的方式来完成相关数据的构造和流转\n- 项目目标\n     - 打通bits、meego、libra、settings、byteconf、codebase等多个平台的，将相关数据进行封装和流转，提供简单的操作界面完成对应的事情\n     - 比如能够依照提测文档【智能】配置好测试环境\n     - 需要做好权限控制\n- 阶段性目标\n     - 考虑这份提测文档：【提测文档】 首页tab-多顶tab-刷新机制优化（主动刷新功能新增+超时自动刷新补齐），测试环境的构造涉及实验和settings，期望阶段性目标能实现【libra did加白】【settings did加白】的原子，并构建一条【用户提供did】->【给libra加白】->【给settings加白】的pipeline，原子需要提供预埋参数的能力，为日后引入LLM智能生成pipeline做准备\n- 该功能与Mock工厂的差异\n     - mock 工厂负责工具基础能力沉淀偏\n单个工具，小说 QM 偏流程化，将这些工具串起来，流转调用。\n- 人力安排（3人）\n\n整理并撰写了功能介绍文档\n\n[Expected Artifacts]\nLark/Feishu Document: 用于撰写和展示整理后的功能介绍文档，内容将包括项目背景、目标、阶段性规划等。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark_creation.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n你是一个具有技术背景的客服专家，现在你会拿到一批客服会话数据，我需要你帮我筛选出其中的技术类问题反馈。\n\n包括但不限于：页面白屏、页面空白、点击没反应、点不动、页面加载太慢等可能是技术 Bug 的反馈。\n\n对于确定是上述问题的进线，可以做一个归类汇总，同时在分类下列出明细，明细包含 VOC 详情、以及能反应该问题的关键信息。请将其中交互功能问题、页面显示问题涉及到的会话明细以表格形式列出来，需要包含会话链接（也就是 VOC 详情），以及能反应是该类问题的会话内容。\n\n[Expected Artifacts]\nLark/Feishu Document: 用于创建包含会话明细表格的结构化文档，可以很好地展示会话链接、问题类型、会话内容等信息，并支持表格格式的清晰展示\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"files.dynamic_13\">\n<title>read data files</title>\n<use_this_knowledge_when>\nwhen read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark_creation.dynamic_35\">\n<title>generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 请基于之前创建的《产品全量用户手册质检规则标准》文档，将评估维度和检查规则汇总成一个规则清单总表。\n\n**任务要求：**\n\n1. **提取和分析现有规则**：\n   - 从工作区中的`产品全量用户手册质检规则标准_v3.lark.md`文件中提取所有评估维度和检查规则\n   - 理解六个核心维度的具体要求和评分标准\n   - 分析100分制评分体系下的违规后果\n\n2. **按指定格式整理成表格**：\n   表格必须包含以下表头：\n   ```\n   | 规则大类 | 规则细则/具体要求 | 限制要求/标准 | 违规后果/处罚措施 | 备注说明 |\n   ```\n\n3. **内容转换规则**：\n   - **规则大类**：将六个评估维度作为规则大类（任务导向性、信息架构、技术准确性与完整性、可读性与可理解性、视觉设计与多媒体支持、可维护性与可扩展性）\n   - **规则细则/具体要求**：将每个维度下的具体检查要点和评估角度转换为明确的规则细则\n   - **限制要求/标准**：明确每个规则的具体标准和阈值要求\n   - **违规后果/处罚措施**：基于100分制评分体系，说明不达标的扣分情况或影响\n   - **备注说明**：添加必要的解释说明或特殊情况处理\n\n4. **特殊要求**：\n   - 保留合规性要求（安全性、图片合规）的一票否决机制\n   - 确保表格内容完整、逻辑清晰\n   - 创建飞书文档作为最终交付物\n\n请确保最终的规则清单总表既保持了原有质检标准的完整性，又以更直观的表格形式呈现，便于实际使用和执行。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"files.dynamic_13\">\n<title>read data files</title>\n<use_this_knowledge_when>\nwhen read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.tool_lark_doc_generation\">\n<title>Feishu/Lark Doc Generation</title>\n<use_this_knowledge_when>\nwhen generating Feishu/Lark documents\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 基于前面对 go-union-i18n 代码仓库的深入分析结果，现在需要完成以下任务：\n\n1. **重构方案设计**：\n   - 基于分析中发现的耦合点、复杂度问题和改进方向，设计具体的重构优化方案\n   - 重点关注降低组件间耦合度、简化复杂决策流程、提升代码可测试性\n   - 设计更清晰的接口抽象和模块划分\n\n2. **代码实现**：\n   - 在新的目录结构中实现重构后的代码\n   - 保持原有功能的完整性，同时优化架构设计\n   - 提供清晰的接口定义和实现分离\n   - 增强代码的可测试性和可维护性\n\n3. **生成Lark文档**：\n   - 创建一个完整的Lark文档，包含：\n     - 原有系统的调用链路分析和实现梳理\n     - 发现的问题和改进点\n     - 详细的重构优化方案设计\n     - 重构前后的对比分析\n     - 重构后的代码架构说明\n   - 使用适当的图表和代码示例来增强文档的可读性\n\n请确保重构方案既保持了原有系统的核心功能，又显著提升了代码的质量和可维护性。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"data_analyze.dynamic_18_aeolus\">\n<title>aeolus tips</title>\n<use_this_knowledge_when>\nWhen the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.planner_19_template\">\n<title>Planning By Lark Template with Default SOP</title>\n<use_this_knowledge_when>\nuse it when user mentioned '参考飞书模板' or '基于飞书模板' or '完成这个模板' or other such words.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.planner_3\">\n<title>llm tasks: data classification, labeling, filter, or other natural language processing tasks tips</title>\n<use_this_knowledge_when>\nperform nlp related tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.planner_15\">\n<title>MR Review Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task involves MR(Merge Request) code review\n</use_this_knowledge_when>\n</item>\n\n<item id=\"SlardarApp.alog_analysis_iOS\">\n<title>iOS alog 智能分析</title>\n<use_this_knowledge_when>\n当用户的输入中包含alog日志分析、结合alog分析反馈问题等相关提示时\n</use_this_knowledge_when>\n</item>\n\n<item id=\"SlardarWeb.slardar_web\">\n<title>Slardar Bug/Standard Process for Frontend Error Fixing or Performance Analyzing</title>\n<use_this_knowledge_when>\nWhen the user mentions the Slardar web scenario.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"SlardarOs.slardar_os\">\n<title>SlardarOS排障场景</title>\n<use_this_knowledge_when>\nWhen the user mentions the Slardar OS scenario.\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n我是飞书妙记媒体侧服务端的校招生员工，目前刚进组2个月了，明天就要跟leader lunch one one了，我没有one one的经验，应该注意哪些事项？下面是我两个月工作的阶段性总结：\n\n两个月工作阶段性总结：\n- 从文件管理模块出发切入了统一拉流项目，梳理了StateLog和RTCLog的写入场景并完成迁移；\n- 测试录制全量case，分配到布局计算相关case，测试并修复了10+个P0级Bug；\n- 根据日志排查，消除重构前后代码Layout diff 和 Rtc diff，取得一定成效,附件是效果图；\n- 与新亮、步威一起开发音频容灾管理模块，其中个人负责开发了音频wav和ref文件上传TOS，以及根据streamkey列出 Tos 上对应目录所有文件功能；\n- 统一拉流项目，部分埋点开发及对应的Grafana面板、Argos告警配置；\n- 音频双声道反向调研，初步调研出解决方案，9月初（目前）进行方案验证。你说的很好，现在，从一个给leader正面印象， 有利于我之后试用期转正及绩效评估（最好能达到M+）的角度出发，结合目前的工作产出情况，我希望你能够给我模拟一个我与我leader交流的场景，让我能随和而不露声色（不显得刻意而急功近利）地达到上述目的。我的leader实际上这两个月并没有直接指导我，我基本上都是由mentor带领的，所有他并不是很清楚我具体做了什么。我希望我与leader之间的对话是长对话，轻松惬意而不失深度。\n\n\n[Expected Artifacts]\nLark/Feishu文档: 提供与leader深度对话的完整指南，包括对话策略、话题准备、沟通技巧和具体的对话框架建议\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"search.dynamic_32\">\n<title>Global Planning and Batch Search Strategy</title>\n<use_this_knowledge_when>\ntask involves search or research\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\nProceed with Phase 2: Content Generation & Visualization. I will start by drafting the content for the report and creating the Mermaid diagram.\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 请继续完成 module_269 的埋点事件分析任务。你已经成功找到了3个 `collector.collect` 调用，现在需要完成详细的事件分析并生成最终的 JSON 文件。\n\n**已找到的事件位置：**\n- `src/uba-ui/Period/creators/createPeriodDropdownButton.ts`\n- `src/uba-ui/Period/creators/createPeriodDashboardDropdownButton.ts`  \n- `src/uba-ui/Period/components/PeriodVs/index.tsx`\n\n**需要完成的任务：**\n\n1. **详细分析每个事件调用**\n   - 提取准确的事件名称（解析变量引用为静态字符串）\n   - 获取完整的原始调用代码\n   - 确定准确的行号和文件路径\n   - 分析调用上下文和触发条件\n   - 对事件进行分类（用户交互、生命周期、业务逻辑、定时/条件触发）\n\n2. **变量解析**\n   - 对于变量引用的事件名称，追踪到常量定义\n   - 如果无法解析，标记为 unresolved_events\n\n3. **生成完整的 JSON 文件**\n   - 严格按照 LABEL_gen2.md 规范生成 `step3_events_analysis_module_269.json`\n   - 包含所有必需字段：module_name, events, event_constants, unresolved_events, summary\n   - 确保 JSON 格式完全符合规范\n\n**输出要求：**\n- 必须生成完整的 `step3_events_analysis_module_269.json` 文件\n- 每个事件都要包含完整的分析信息\n- 提供清晰的总结说明\n\n请仔细分析每个找到的事件调用，确保分析的准确性和完整性。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n任务已全部完成！成功创建了包含所有156个权限操作码的完整技术参考文档，并生成了飞书文档供用户查阅。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n角色\n你是一个专注于生活服务电商领域平台治理的资深产品经理专家，拥有丰富的业务理解力和实战经验，专门处理PRD（产品需求文档）的完善和优化工作，能够将复杂的业务需求转化为清晰、可执行的产品文档。\n工作流程\n输入获取：接收用户提供的PRD草稿或需求描述，确保成功理解业务背景、目标用户、核心功能需求，并准备进行分析和完善。\n\n\n分析/处理逻辑：\n业务维度：深入分析生活服务电商的业务模式，治理团队的核心痛点和机会点。\n功能维度：梳理功能需求的完整性、逻辑性和可行性，确保功能设计符合业务目标和用户需求。\n技术维度：评估技术实现的复杂度和可行性，提供清晰的可落地方案及交互说明\n运营维度：考虑运营使用便捷、产品能力好理解等运营层面的需求。\n管理维度：确保文档结构清晰、表达准确，便于不同角色理解和执行。\n结果输出：根据分析结果，输出结构完整、逻辑清晰的PRD文档，必要时补充产品逻辑图，确保运营和老板能够理解，研发能够执行。飞书文档链接私密，仅我可看，我为管理员。\n\n\n规则/分析维度\n规则1：结构完整性\nPRD必须包含：需求背景（业务痛点、解题思路、业务目标）、产品方案简述（用户画像及对应的核心产品功能，用户动线、产品逻辑及架构）、需求详情（每个功能模块的具体要求，越详细越好）、运营策略或机制，项目计划等核心模块\n每个模块内容详实，逻辑清晰，避免遗漏关键信息\n规则2：表达清晰性\n使用标准化的产品文档格式，但信息不要冗余，一级目录与我的原始prd结构大体保持一致即可\n功能描述具体、明确，避免模糊表达\n关键流程配备流程图或逻辑图\n重要概念提供明确定义\n规则3：可执行性\n功能需求具备明确的字段说明、逻辑说明、交互说明\n运营策略具备可操作的执行步骤\n项目计划包含明确的时间节点和责任人\n规则4：业务适配性\n深度结合生活服务电商的行业特点\n充分考虑平台治理的特殊需求（如商家管理、服务质量控制、用户体验优化等）\n方案设计符合监管要求和行业最佳实践\n规则5：沟通有效性\n面向运营和管理层：重点突出业务痛点、业务价值、解题思路，需要清晰简洁好理解。\n面向研发团队：提供详细的功能描述、交互描述，不要过度简化我的prd内容。\n使用图表、流程图等可视化工具辅助理解\n\n[Expected Artifacts]\nFeishu/Lark文档: 用于生成结构完整、逻辑清晰的PRD产品需求文档，包含需求背景、产品方案、功能详情、运营策略等完整模块，面向运营、管理层和研发团队\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 你之前的分析遇到了搜索工具的问题。现在请重新分析 module_296，采用以下方法：\n\n1. **明确任务目标**：你需要创建 step3_events_analysis_module_296.json 文件，而不是寻找已存在的文件\n\n2. **手动文件检查**：由于 grep_search 工具可能不可靠，请手动检查 module_296 中的关键文件：\n   - 从 step1_modules_split.json 中已知 module_296 包含30个文件，都在 src/uba-utils/meta/hooks/ 目录下\n   - 这些文件主要是 React hooks 工具函数\n   - 手动打开几个代表性文件，查看是否包含：\n     - Tea SDK 直接调用（tea.event, Tea.event 等）\n     - 封装函数调用（如 track(), logEvent(), collector.collect() 等）\n\n3. **基于实际情况生成 JSON**：\n   - 如果确实没有发现埋点事件调用，生成一个包含空事件列表的 JSON 文件\n   - JSON 格式必须严格按照 LABEL_gen2.md 规范：\n     ```json\n     {\n       \"module_name\": \"module_296\",\n       \"events\": [],\n       \"event_constants\": {},\n       \"unresolved_events\": [],\n       \"summary\": {\n         \"total_events\": 0,\n         \"resolved_events\": 0,\n         \"unresolved_events\": 0,\n         \"files_with_events\": 0,\n         \"total_files_analyzed\": 30\n       }\n     }\n     ```\n\n4. **保存文件**：将生成的 JSON 保存为 step3_events_analysis_module_296.json\n\n请立即开始执行，不要再依赖可能失效的搜索工具，直接手动检查文件内容。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"search.dynamic_32\">\n<title>Global Planning and Batch Search Strategy</title>\n<use_this_knowledge_when>\ntask involves search or research\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 基于用户的要求，需要完善豆包教育未来半年规划提纲框架文档的现状分析部分：\n\n1. **读取源文档内容**：\n   - 仔细阅读workspace中的\"豆包教育_业务OnePage（WIP）.lark.md\"文档\n   - 识别其中的关键技术现状信息和架构图\n\n2. **内容摘抄和整合**：\n   - 从源文档中摘抄适合放在现状分析部分的内容\n   - 特别关注架构图，确保完整复制到目标文档\n   - 选择与豆包教育业务相关的技术现状描述\n\n3. **更新提纲框架文档**：\n   - 在现有的提纲框架文档基础上，完善\"2. 现状分析\"部分\n   - 将摘抄的内容和架构图合理地整合到相应的子章节中\n   - 保持原有的编号体系和提纲结构\n\n4. **输出要求**：\n   - 更新现有的Feishu/Lark提纲框架文档\n   - 确保架构图能正确显示\n   - 保持文档的整体格式和风格一致性\n\n请基于现有的提纲框架文档进行内容补充，重点是现状分析部分的完善。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_40\">\n<title>Image Search or Use Guidelines</title>\n<use_this_knowledge_when>\nwhen using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_9\">\n<title>progress example - general</title>\n<use_this_knowledge_when>\nit should always be used for better planning.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n\n## Objective:\n    将所有生成的 Markdown 文档和图片，整合成一份结构清晰的飞书（Lark）云文档，作为最终交付物。\n\n## Actions(You should adapt the actions to the current task):\n    1.  使用 `lark_creation` 工具，以上述聚合文件为内容，创建一个标题为“Aime 项目后端完整技术方案”的飞书云文档:\n      ** 按照逻辑顺序（需求分析 -> 架构 -> ERD -> ... -> 改造计划），将所有 `.md` 文件的内容聚合到该临时文件中，并调整标题层级\n      ** 确保图片引用路径正确\n    2.  输出新创建的飞书云文档链接。\n\n## Deliverable:\n- [最终技术方案飞书文档] (Lark document URL)\n\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"files.dynamic_13\">\n<title>read data files</title>\n<use_this_knowledge_when>\nwhen read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.tool_lark_doc_generation\">\n<title>Feishu/Lark Doc Generation</title>\n<use_this_knowledge_when>\nwhen generating Feishu/Lark documents\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.tool_lark_progress_example_newbie\">\n<title>progress example - Lark</title>\n<use_this_knowledge_when>\nwhen the task needs designing and generating lark reports or documents, be aware that this is only for new lark document creation.\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 作为一名资深的技术架构师，请仔细审查并分析提供的技术方案文档 `Rax_（USB）连接优化方案.lark.md`。\n\n你的任务是：\n1.  **阅读并理解**：完整阅读 `Rax_（USB）连接优化方案.lark.md` 的内容，理解其核心目标、现有问题和提出的解决方案。\n2.  **批判性分析**：从技术架构、系统设计、可扩展性、安全性、可维护性和用户体验等多个维度，对方案进行深入分析。\n3.  **撰写分析报告**：基于你的分析，创建一个新的飞书文档。报告必须包含以下三个核心部分，并进行详细阐述：\n    *   **存在的问题**：明确指出当前方案中可能存在的设计缺陷、技术风险或不合理的假设。\n    *   **遗漏的点**：补充方案中未能覆盖到的重要方面，例如：异常处理、监控告警、向后兼容性、成本效益分析或未来的可扩展性等。\n    *   **可以优化的点**：提出具体的、可行的优化建议，以提升方案的性能、稳定性、开发效率或降低成本。\n\n最终产物应为一份结构清晰、论点明确、建议具体的专业技术评审报告。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"files.dynamic_13\">\n<title>read data files</title>\n<use_this_knowledge_when>\nwhen read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n\n## Objective:\n    解析 `https://code.byted.org/ti/dsa_sched_strategy/merge_requests/97`，使用 `codebase` 工具获取 MR 的详细信息，特别是源分支和目标分支的 Commit ID。然后，使用 Git 命令将本地仓库设置为待审查的代码状态。\n\n## Actions(You should adapt the actions to the current task):\n    1.  **解析 MR URL**：从 `https://code.byted.org/ti/dsa_sched_strategy/merge_requests/97` 中提取仓库 ID 和 MR 编号。\n    2.  **获取 MR 详情**：调用 `codebase.get_merge_request` 工具，传入仓库 ID 和 MR 编号，获取 MR 的元数据。\n    3.  **提取并保存 Commit ID**：从返回结果中解析出 `source_commit_id` 和 `base_commit_id`。将这两个 ID 保存到一个临时工作文件（如 `commit_ids.json`）中，以便后续步骤使用。\n    4.  **准备本地仓库**：\n        -   进入本地代码仓库目录。\n        -   执行 `git fetch` 拉取最新的远程对象。\n        -   执行 `git checkout [source_commit_id]`，将工作区切换到 MR 源分支的最新代码状态。\n\n## Deliverable:\n- [本地代码仓库] (已切换到待审查的 commit)\n- [Commit ID 文件] (e.g., commit_ids.json)\n\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego tool principle</title>\n<use_this_knowledge_when>\nwhen working with meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_4_html_citation\">\n<title>HTML Citation and Reference Management</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n **🚨 MANDATORY FIRST**: 你的第一个操作必须是使用lark_download工具下载用户提供的参考文档。在完成这个操作之前，不要进行任何其他操作。\n\n用户发现了一个重要的数据问题：当前图表中显示站内CTR互动类比IM类高，这与用户的认知不符。需要核实数据并修正：\n\n## 核心任务要求\n\n### 1. 数据核实和分析\n- **下载参考文档**：https://bytedance.larkoffice.com/docx/MPBHddrFeoNX5lxNNitco1x5niy\n- **仔细分析文档中的CTR数据**：\n  - 站内推送各类型的真实CTR数据\n  - 站外推送各类型的真实CTR数据\n  - 特别关注IM类和互动类的CTR对比\n- **数据交叉验证**：与之前收集的其他文档数据进行交叉验证\n\n### 2. 问题识别和修正\n- **识别数据差异**：\n  - 当前图表显示的数据vs参考文档的数据\n  - 找出具体的数据错误点\n  - 分析可能的数据来源混淆原因\n- **数据修正**：\n  - 基于参考文档的准确数据\n  - 确保IM类和互动类CTR的正确关系\n  - 验证其他类型（发文、推人）的数据准确性\n\n### 3. 图表重新生成\n- **重新生成站内推送图表**：\n  - 使用准确的CTR数据\n  - 确保IM类CTR数据的准确性\n  - 保持图表的清晰标注和内部化风格\n- **重新生成站外推送图表**：\n  - 基于参考文档的准确数据\n  - 确保各类型推送CTR的正确展示\n- **数据一致性检查**：确保新图表数据逻辑合理\n\n### 4. 文档更新\n- **更新飞书文档**：\n  - 替换错误的图表\n  - 修正相关的数据引用和说明\n  - 确保文档中所有CTR数据的一致性\n- **保持文档结构**：\n  - 不改变整体结构和逻辑\n  - 仅修正数据错误部分\n  - 保持其他优化成果\n\n### 5. 数据验证和说明\n- **数据来源说明**：\n  - 在文档中标注数据来源\n  - 说明修正的原因和依据\n- **逻辑一致性检查**：\n  - 确保修正后的数据支持原有的分析结论\n  - 如有必要，调整相关的分析表述\n\n### 6. 质量保证\n- **准确性验证**：确保所有数据来源可靠\n- **可读性保持**：修正后的图表仍然清晰易读\n- **专业性维护**：保持文档的专业水准\n\n请优先处理这个数据准确性问题，确保图表和文档中的CTR数据完全准确。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n首先查看现有项目结构，然后创建 metrics 包的目录结构和核心文件\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_40\">\n<title>Image Search or Use Guidelines</title>\n<use_this_knowledge_when>\nwhen using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n基于已有的关键词列表和需求分析，制定针对4个重点领域的全面搜索策略，准备执行批量技术文档搜索\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_40\">\n<title>Image Search or Use Guidelines</title>\n<use_this_knowledge_when>\nwhen using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 解耦重构的主要工作已经完成，现在需要完成最后的验证和提交步骤：\n\n**主要任务：**\n\n1. **Git状态检查和代码提交**\n   - 检查当前git状态，查看所有修改的文件\n   - 创建合适的分支（如果需要）\n   - 提交解耦重构的代码变更，使用清晰的commit message\n   - 准备创建Merge Request（如果用户需要）\n\n2. **最终验证报告**\n   - 总结本次解耦重构的完成情况\n   - 列出成功处理的文件和依赖关系\n   - 说明遗留问题：AgeGraduationUtil.kt需要人工处理\n   - 提供后续建议\n\n3. **问题汇报**\n   - 明确说明需要用户人工处理的具体问题\n   - 解释为什么某些问题需要人工介入\n   - 提供解决建议\n\n**重要说明：**\n- 由于构建环境缺少ANDROID_HOME配置，编译验证无法完成，但代码修改符合规范\n- 需要向用户说明AgeGraduationUtil.kt文件的logger定义问题\n- 准备完整的工作总结，包括已完成和待处理的项目\n\n请完成这些最终步骤，并准备向用户汇报整个解耦任务的执行结果。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"data_analyze.2\">\n<title>LLM Guide for Data Analysis and Classification</title>\n<use_this_knowledge_when>\nProcess text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_18_aeolus\">\n<title>aeolus tips</title>\n<use_this_knowledge_when>\nWhen the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_1\">\n<title>data analysis working principles</title>\n<use_this_knowledge_when>\nwhen processing or analyzing data (including git history, maps, tables, etc.)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_3\">\n<title>table data processing principles</title>\n<use_this_knowledge_when>\nwhen need process/filter/generate excel/csv data files\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n You need to use the GEC搜索工具v2_18 (automatic evaluation tool) to evaluate the first 90 queries from the provided evaluation link. The task involves:\n\n1. **Data Acquisition**: Access the evaluation link `https://search-og-us.tiktok-row.net/garr/fetch_review/shot_history?eid=585432036672587&name=%E3%80%90US%20relevance%E3%80%91union_smr%20&app_id=265652` and extract the first 90 queries.\n\n2. **Automatic Evaluation**: Use the GEC搜索工具v2_18 to perform relevance evaluation on the top 6 results for each query. The evaluation should focus on the relevance dimension as specified.\n\n3. **Process Flow**: Follow the three-step process of the GEC tool:\n   - Use `get_tids_from_url()` to generate the tid list for evaluation\n   - Use `sbs_tids()` to evaluate each tid and get individual results\n   - Use `generate_relevance_conclusion()` to aggregate all results and generate conclusions\n\n4. **Data Output**: Save all processed query data and evaluation results in structured files for later report generation.\n\nThe evaluation should cover relevance assessment for the top 6 search results of each of the first 90 queries from the provided link. \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"data_analyze.2\">\n<title>LLM Guide for Data Analysis and Classification</title>\n<use_this_knowledge_when>\nProcess text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_18_aeolus\">\n<title>aeolus tips</title>\n<use_this_knowledge_when>\nWhen the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_1\">\n<title>data analysis working principles</title>\n<use_this_knowledge_when>\nwhen processing or analyzing data (including git history, maps, tables, etc.)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_25_2\">\n<title>guide for preparing content for lark templates reports</title>\n<use_this_knowledge_when>\nuse it whenever/wherever preparing content for a lark template or markdown report, analyzing data or generating visualization part for reports, even if the current task is just creating intermediate artifacts or doing analysis that will later be integrated into a final document.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"data_analyze.dynamic_3\">\n<title>table data processing principles</title>\n<use_this_knowledge_when>\nwhen need process/filter/generate excel/csv data files\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"search.dynamic_26\">\n<title>search must have knowledge</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if the following steps need use search tool or research tool.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"search.dynamic_32\">\n<title>Global Planning and Batch Search Strategy</title>\n<use_this_knowledge_when>\ntask involves search or research\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 请帮我处理这个Bilibili视频链接：https://www.bilibili.com/video/BV1Lm4y1273Q/?spm_id_from=333.1387.upload.video_card.click\n\n任务要求：\n1. 访问该Bilibili视频，获取视频的基本信息（标题、时长、描述、UP主等）\n2. 提取视频中的音频内容和字幕信息（如果有的话）\n3. 将视频内容转录成文本\n4. 整理成结构化的文档，包括：\n   - 视频基本信息\n   - 内容要点总结\n   - 详细内容整理\n   - 关键概念或术语解释（如适用）\n5. 最终生成飞书文档格式，便于阅读和分享\n\n请确保内容准确，结构清晰，方便读者理解。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n  .withColumn('is_duplicate', <PERSON><PERSON>coalesce(<PERSON>.col('is_duplicate'), 0))   File \"/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/functions.py\", line 925, in coalesce   File \"/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py\", line 61, in _to_seq   File \"/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py\", line 61, in <listcomp>   File \"/opt/tiger/yodel/container/pyspark.zip/pyspark/sql/column.py\", line 49, in _to_java_column TypeError: Invalid argument, not a string or column: 0 of type <class 'int'>. For column literals, use 'lit', 'array', 'struct' or 'create_map' function.\n\n[Expected Artifacts]\nDirect Answer: The user has provided a traceback and needs an explanation of the error and a solution. A direct text answer is the most appropriate and efficient format for this kind of debugging help.\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n详细介绍一下VIPER架构\n\n\n[Expected Artifacts]\nLark/Feishu Document: 用于撰写关于VIPER架构的详细介绍报告，包括其核心组件、数据流、优缺点等内容。\nDiagrams: 用于创建一个可视化图表，清晰地展示VIPER架构中各个组件（View, Interactor, Presenter, Entity, Router）之间的关系和交互流程。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n开始Phase 1的最后一步：检查目标文件是否存在并分析其内容结构，确保我们有正确的源文件来创建Lark文档。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_18\">\n<title>Planning Example</title>\n<use_this_knowledge_when>\nalways\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_1\">\n<title>rich format file convert tips</title>\n<use_this_knowledge_when>\nwhen user provide rich format files(such as pdf, microsoft office files, etc.) and need to understand. or need to convert file format.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_21\">\n<title>Text-to-Speech (TTS) capability knowledge</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when user requests text-to-speech conversion.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_23\">\n<title>Word Document Processing Capability</title>\n<use_this_knowledge_when>\nWhen user asks to read, extract, or convert Word documents\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_24\">\n<title>Batch Processing Capability for Mewtwo</title>\n<use_this_knowledge_when>\nWhen tasks involve repetitive operations that can benefit from batch processing - multiple similar operations on different inputs.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"planner.planner_25\">\n<title>Data Visualization</title>\n<use_this_knowledge_when>\nthe user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_12\">\n<title>Plan guide for tasks operations on specific website</title>\n<use_this_knowledge_when>\nwhen user query contains task that is related to downloading data from specific website, exporting data, etc.\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n给我详细讲讲Spark的概念、原理、使用场景、最佳实践，以及基本的操作讲解。多来几张图讲解\n细说用户的sql是怎么转成 RDD、DataFrame并构造DAG的细说 Spark 的 DataFrame、DataSet、RDD，在原理层面和使用层面细说 Spark 的 DataFrame、DataSet、RDD，在原理层面和使用层面，以及他们和真实物理存储的映射关系\n\n[Expected Artifacts]\nLark/Feishu Document: 用于撰写关于 Spark DataFrame、DataSet 和 RDD 的详细分析报告，包含原理、使用方法和存储映射等结构化内容。\nDiagrams: 用于绘制图表，以可视化方式解释 RDD、DataFrame、DataSet 的内部原理、相互关系以及与物理存储的映射关系，并嵌入到文档中。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_40\">\n<title>Image Search or Use Guidelines</title>\n<use_this_knowledge_when>\nwhen using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n首先阅读项目的 README.md 文件，了解项目的基本概述、主要特性和使用指南\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n任务已完成，成功生成了包含变更概述、实现特点、代码质量评估、潜在影响分析和改进建议的结构化代码分析报告\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"files.dynamic_13\">\n<title>read data files</title>\n<use_this_knowledge_when>\nwhen read data files(wherever they come from), especially data files like csv, large txt, large json/jsonl, excel, etc.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.tool_lark_doc_generation\">\n<title>Feishu/Lark Doc Generation</title>\n<use_this_knowledge_when>\nwhen generating Feishu/Lark documents\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 请分析用户提供的JSON配置代码，这看起来是一个隐私保护相关的服务配置。需要你：\n\n1. **结构解析**: 分析整个JSON配置的层次结构和组织方式\n2. **字段解读**: 详细解释各个字段的含义和作用，包括但不限于：\n   - version_id, psm等基础配置\n   - method_configs中的认证阶段、策略配置\n   - policy_config中的认证点、实体、策略详情\n   - attributes中的属性定义和类型\n   - action_config中的操作配置\n   - traffic_control中的流量控制\n   - log_config中的日志配置\n3. **业务逻辑分析**: 解释这个配置实现的业务功能和隐私保护机制\n4. **生成报告**: 将分析结果整理成一份结构化的Feishu/Lark文档\n\n配置代码：\n```json\n{\n    \"version_id\": 33,\n    \"psm\": \"aweme.privacy.minix_kitex_callee_demo\",\n    \"method_configs\": [\n        {\n            \"method\": \"GetUserD\",\n            \"auth_stage\": 2,\n            \"policy_config\": [\n                {\n                    \"auth_point\": \"GetUserResponse\",\n                    \"entity\": \"User\",\n                    \"policies\": [\n                        {\n                            \"id\": 1001,\n                            \"name\": \"user_id\",\n                            \"policy_type\": 2,\n                            \"attributes\": [\n                                {\n                                    \"id\": 1,\n                                    \"name\": \"user_id\",\n                                    \"attr_type\": 1,\n                                    \"value_type\": 4,\n                                    \"local_field\": {\n                                        \"name\": \"user_id\",\n                                        \"type\": 4,\n                                        \"expr\": \"fe_user_id\",\n                                        \"auth_point\": \"GetUserRequest\"\n                                    }\n                                },\n                                {\n                                    \"id\": 2,\n                                    \"name\": \"is_aweme\",\n                                    \"attr_type\": 3,\n                                    \"value_type\": 3,\n                                    \"func\": \"privacy.minix_test_feature_bool\"\n                                }\n                            ],\n                            \"exprs\": [\n                                \"is_aweme == true\"\n                            ],\n                            \"action_config\": {\n                                \"default_config\": [\n                                    {\n                                        \"action_type\": 4,\n                                        \"operate_fields\": [\n                                            \"GetUserResponse.User.ShortId\"\n                                        ]\n                                    }\n                                ]\n                            },\n                            \"traffic_control\": {\n                                \"switch\": true,\n                                \"sample_prop\": 10000,\n                                \"block_prop\": 10000,\n                                \"timeout\": 30\n                            }\n                        }\n                    ]\n                }\n            ],\n            \"log_config\": {\n                \"default_log_config\": {\n                    \"local\": {\n                        \"prop\": 10000,\n                        \"err_prop\": 10000,\n                        \"level\": [\n                            \"Info\",\n                            \"Notice\",\n                            \"Warn\",\n                            \"Error\",\n                            \"Fatal\"\n                        ]\n                    },\n                    \"remote\": {\n                        \"prop\": 10000,\n                        \"err_prop\": 10000\n                    },\n                    \"metric\": 10000\n                }\n            },\n            \"enable_ticket\": true\n        }\n    ]\n}\n```\n\n请生成一份详细的Feishu/Lark文档，帮助用户快速理解这段配置代码的含义和功能。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego tool principle</title>\n<use_this_knowledge_when>\nwhen working with meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n分析现有的SubmissionRecordServiceTest.java来了解正确的包路径和导入模式，然后为SubmissionService.java和AppletController.java生成正确的单元测试\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.planner_15\">\n<title>MR Review Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task involves MR(Merge Request) code review\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n  原始提示词\n目前我们有一个iOS的代码仓库，请协助我完成一份能让LLM高效理解并执行的markdown规则，规则的主要内容为：根据下述规则，生成符合规范的UI代码\n输入内容会带有figma链接，输出内容是对应的UI代码，且需要符合下面的规范\n- 自动布局相关，使用snapkit\n\n[Expected Artifacts]\nMarkdown Document: 用于编写和展示给LLM的UI代码生成规则。\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n首先阅读需求文档和效果图，理解具体的功能需求\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n继续深入分析权限设置面板中的具体组件，查找可能存在条件性 Hooks 调用、循环中的 Hooks、动态 Hooks 数量等违反 Hooks 规则的情况\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n继续分析其他核心文件的变更，包括统一验证管理器、风控管理器、支付流程控制器等关键模块，然后开始对照技术方案文档进行符合性审查\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.coder_4\">\n<title>Collaboration and Version Control Best Practices</title>\n<use_this_knowledge_when>\nwhen working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_14_2\">\n<title>validate generated images</title>\n<use_this_knowledge_when>\nwhen generated images, to check the rendered content as expected, no errors, no rendering issues.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_27\">\n<title>PDF file creation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge if you need create a PDF file.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_36\">\n<title>Project Structure and Code Organization</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.dynamic_8\">\n<title>meego principle</title>\n<use_this_knowledge_when>\nwhen task is about meego\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.15\">\n<title>time conversion tips</title>\n<use_this_knowledge_when>\nUse this when involving time processing.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.17\">\n<title>PDF Processing Guidelines</title>\n<use_this_knowledge_when>\nworking with PDF documents that require efficient reading and information extraction.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.19\">\n<title>python json save/parse tips</title>\n<use_this_knowledge_when>\nwhen using python json lib to save or parse json data.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.3\">\n<title>ECharts Tips</title>\n<use_this_knowledge_when>\nwhen using echarts\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.5\">\n<title>display maps in web browser tips</title>\n<use_this_knowledge_when>\nwhen display or draw maps in web browser/frontend site.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.6\">\n<title>python pandas lib tips</title>\n<use_this_knowledge_when>\nwhen using python pandas lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.7\">\n<title>python plotly lib tips</title>\n<use_this_knowledge_when>\nwhen using python plotly lib.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_1\">\n<title>Coding Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code in any programming language\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_3\">\n<title>Testing and Debugging Best Practices</title>\n<use_this_knowledge_when>\nwhen writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_7\">\n<title>Media Guidelines</title>\n<use_this_knowledge_when>\nwhen creating documents, presentations, or website\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.coder_8\">\n<title>general html generation principles</title>\n<use_this_knowledge_when>\nwhen generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_16\">\n<title>slardar bug fix standard</title>\n<use_this_knowledge_when>\nWhen the user mentions Slardar scenarios\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_18\">\n<title>Loop Tool Execution for Efficiency</title>\n<use_this_knowledge_when>\nExecute batch tool calling using loop tools to save time and reduce manual effort.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_28\">\n<title>Text-to-Speech (TTS) implementation guidelines</title>\n<use_this_knowledge_when>\nYou MUST use this knowledge when implementing text-to-speech conversion tasks.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_31\">\n<title>pandoc document conversion tool</title>\n<use_this_knowledge_when>\ntask involves converting Word documents to markdown format\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_33\">\n<title>Document Writing Guidelines</title>\n<use_this_knowledge_when>\ntask involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_37\">\n<title>PlantUML guidelines and restrictions</title>\n<use_this_knowledge_when>\nwhen need to draw PlantUML diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_38_chart_with_python\">\n<title>data visualization with Python preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_39_data_visualization\">\n<title>data visualization preferences</title>\n<use_this_knowledge_when>\nvisualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.dynamic_40\">\n<title>Image Search or Use Guidelines</title>\n<use_this_knowledge_when>\nwhen using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory\n</use_this_knowledge_when>\n</item>\n\n<item id=\"mewtwo.planner_16\">\n<title>mermaid guidelines and tips</title>\n<use_this_knowledge_when>\nwhen need to draw mermaid diagrams\n</use_this_knowledge_when>\n</item>\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n 基于之前分析的TikTok Shop新商半托管项目内容，深入分析和阐述\"平台批量化服务\"的概念和实现模式。请创建一份详细的分析文档：\n\n## 分析要求：\n\n### 1. 平台批量化服务的定义和概念\n- **核心概念**：什么是平台批量化服务\n- **服务模式**：与传统一对一服务的区别\n- **平台角色**：平台在批量化服务中的作用和价值\n- **批量化特征**：如何实现规模化和标准化\n\n### 2. 服务架构和实现方式\n- **技术架构**：平台如何支撑批量化服务\n- **业务流程**：从商家需求到服务交付的完整流程\n- **资源配置**：如何高效配置和管理服务商资源\n- **质量控制**：批量化服务的质量保障机制\n\n### 3. 核心特点和优势\n- **规模效应**：批量化带来的成本和效率优势\n- **标准化服务**：统一的服务标准和交付质量\n- **平台保障**：平台提供的信用背书和风险控制\n- **资源优化**：服务商资源的最优配置和利用\n\n### 4. 具体实现案例\n基于TikTok Shop新商半托管项目，说明：\n- **商家端体验**：商家如何享受批量化服务\n- **服务商端运作**：服务商如何参与批量化服务\n- **平台端管理**：平台如何协调和管理整个服务体系\n- **效果和价值**：批量化服务带来的实际效果\n\n### 5. 与传统服务模式的对比\n- **传统模式痛点**：一对一服务的局限性\n- **批量化优势**：平台批量化服务的改进和创新\n- **价值创造**：为各方参与者创造的独特价值\n- **市场意义**：对行业发展的推动作用\n\n## 输出要求：\n\n### 1. 内容深度\n- 从商业模式、技术架构、运营管理等多维度分析\n- 结合具体的项目案例进行说明\n- 突出平台批量化服务的创新性和价值\n\n### 2. 文档格式\n- 创建专业的Word文档和Markdown文件\n- 使用图表和流程图辅助说明（如需要）\n- 结构清晰，逻辑严谨\n\n### 3. 目标受众\n- 面向对平台服务模式感兴趣的商业人士\n- 适合用于项目介绍和商业分析\n- 可作为平台服务模式的参考文档\n\n请基于项目实际情况，深入分析平台批量化服务的核心要素和实现模式。 \n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"developer.coder_2\">\n<title>Code Exploring and Understanding</title>\n<use_this_knowledge_when>\nwhen analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_5\">\n<title>Frontend Development Workflow Without A Specified Repository</title>\n<use_this_knowledge_when>\nwhen developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_6\">\n<title>Fullstack App Development Workflow</title>\n<use_this_knowledge_when>\nwhen user requests to build a web app that needs a backend service\n</use_this_knowledge_when>\n</item>\n\n<item id=\"developer.coder_9\">\n<title>Get Frontend Development Knowledge</title>\n<use_this_knowledge_when>\nWhen you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.\n\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n准备文件内容分析 - 为每个待审查文件提取具体代码变更，创建详细的代码变更分析，为后续的专业代码审查做好准备\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}, {"msg": [{"content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"argos.planner_22\">\n<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>\n<use_this_knowledge_when>\nWhen the user mentions Argos scenario or provides a `logid` for diagnosis.\n</use_this_knowledge_when>\n</item>\n\n<item id=\"bits_analysis.planner_14\">\n<title>Bits Analysis Platform Guidelines</title>\n<use_this_knowledge_when>\nonly if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform\n</use_this_knowledge_when>\n</item>\n\n<item id=\"browser.planner_10\">\n<title>browser use guidelines</title>\n<use_this_knowledge_when>\nYou need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.\n\n</use_this_knowledge_when>\n</item>\n\n<item id=\"cosy.dynamic_30\">\n<title>Codebase Search Guides for Local Repositories</title>\n<use_this_knowledge_when>\nUsed when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories\n</use_this_knowledge_when>\n</item>\n\n<item id=\"devops_agent.devops_agent_2\">\n<title>devops agent in planner</title>\n<use_this_knowledge_when>\nThe task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。\n</use_this_knowledge_when>\n</item>\n\n<item id=\"lark.planner_7\">\n<title>read or generate Feishu/Lark doc</title>\n<use_this_knowledge_when>\nthe user's request involves creating or reading a Feishu/Lark doc\n</use_this_knowledge_when>\n</item>\n\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>\n\n<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>\n<id>20</id>", "role": "system"}, {"content": "User's task:\n<task>\n## 角色设定\n你是资深Go架构师，按Google SRE标准对代码进行严格审查。\n\n## 任务目标\n审查GitHub PR的代码变更：https://github.com/apache/answer/pull/1265\n\n## 执行步骤\n\n### 1. 获取代码示例\n```bash\ngit clone https://github.com/apache/answer/\ncd answer\ngit fetch origin pull/1265/head:pr-1265\ngit diff origin/main...pr-1265 > diff.txt\n```\n\n### 2. 审查维度\n- **架构**: SOLID原则、设计模式、技术债务\n- **质量**: 圈复杂度>10、重复代码、并发安全\n- **性能**: 时间复杂度、内存泄漏、N+1查询\n- **安全**: OWASP风险、输入验证、权限检查\n- **维护**: 文档、日志、错误处理\n\n### 3. 输出格式\n```\n文件: path/to/file.go @L行号\n\n[🔴严重/🟡警告] 问题类型\n问题描述 (CWE-XXX)\n影响: 性能★★☆ 安全★★★ 维护★☆☆\n修复建议:\n```go\n// 改进代码\n```\n\n## 要求\n- 跳过测试文件和依赖注入代码\n- 每个diff块独立分析\n- 仅输出有问题的代码\n- 提供具体修复方案\n- 最终以文档格式输出审查报告\n- 确认给出的CWE-编号是确实存在的，且意义正确\n\n[Expected Artifacts]\nFeishu/Lark文档: 用于呈现按Google SRE标准完成的PR代码审查报告，包含问题分级、CWE映射、修复示例及维护建议\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.\n\n- If the knowledge's used_when is relevant or potentially useful, choose it.\n- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.\n- Even for simple tasks, knowledge may be useful for handling unexpected situations.\n- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.\n\nDO NOT skip reviewing any single knowledge item, review one by one.", "role": "user"}]}]