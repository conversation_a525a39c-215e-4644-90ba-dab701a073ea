prompt
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
I will now proceed with Phase 1 and 2, which involves confirming the file and creating the Lark document in a single step.
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
创建结构化的飞书宣讲文档，包含四个主要部分：业务内容、算法应用、质量保障体系、后续规划建设
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark_creation.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
https://www.shgjj.com/html/gzfzl/fyzs/215909.html
帮我把这个网页里面的简介，做成一个pdf 发给我


[Expected Artifacts]
Other: 用户要求将网页内容制作成一个PDF文件。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
创建完整的使用文档，包括API文档、使用示例和与Mockito的对比文档，完善整个Java Mock框架项目
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 用户对上一个图表不满意，提出了新的要求。请根据用户提供的Lark表格链接，重新生成一个对比柱状图。

**Lark表格链接**: `https://bytedance.larkoffice.com/sheets/PiEyswGWDhDoTOtxDEocXmG9nfh?sheet=96198a`

**新的图表要求**:
1.  **下载并处理数据**: 和上次一样，下载并读取表格数据，统计每个“二级业务线”在不同月份出现的次数。
2.  **生成新的柱状图**:
    *   **横轴 (X-axis)**: **必须以“二级业务线”作为主分组**。
    *   **组内对比**: 在每个“二级业务线”的分组下，并列展示三个不同月份（1月、2月、3月）的柱子，用于对比。
    *   **纵轴 (Y-axis)**: 对应各月份的数量。
    *   **图例 (Legend)**: 必须包含图例，用以区分不同月份的柱子。
3.  **保存结果**: 将新生成的图表保存为图像文件（例如 `business_line_comparison_v2.png`）。

**重要提示**:
- 这次的关键是横轴的分组方式。确保图表结构是“二级业务线” -> “月份对比”，而不是反过来。
- 使用 pandas 的 `groupby` 和 `unstack` 功能可以很方便地重塑数据以适应这种图表格式。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
基于分析的代码变更，生成全面的单元测试，重点测试新增的分环节上报指标功能
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_4_html_citation"">
<title>HTML Citation and Reference Management</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
Commit the changes with the specified message.
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""argos.planner_22"">
<title>Argos Log Diagnosis / LogID Log Interpretation Standard Procedure</title>
<use_this_knowledge_when>
When the user mentions Argos scenario or provides a `logid` for diagnosis.
</use_this_knowledge_when>
</item>

<item id=""bits_analysis.planner_14"">
<title>Bits Analysis Platform Guidelines</title>
<use_this_knowledge_when>
only if the user task explicitly fixing issues specifically with the `Bits Analysis` Platform
</use_this_knowledge_when>
</item>

<item id=""browser.planner_10"">
<title>browser use guidelines</title>
<use_this_knowledge_when>
You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.

</use_this_knowledge_when>
</item>

<item id=""cosy.dynamic_30"">
<title>Codebase Search Guides for Local Repositories</title>
<use_this_knowledge_when>
Used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories
</use_this_knowledge_when>
</item>

<item id=""devops_agent.devops_agent_2"">
<title>devops agent in planner</title>
<use_this_knowledge_when>
The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
</use_this_knowledge_when>
</item>

<item id=""lark_creation.planner_7"">
<title>read or generate Feishu/Lark doc</title>
<use_this_knowledge_when>
the user's request involves creating or reading a Feishu/Lark doc
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
https://code.byted.org/content/hornbill_outsourcing_api 
评价一下这个仓库中lark user token的设计，哪里设计的好，哪里做的不好。
另外目前有一个race condition的问题，两个请求同时命中了redis中的过期token，然而refresh_token只能使用一次，导致另一个请求失败，帮我修复他我现在需要用 FaaS来做一个单例定时器，主动刷新快过期的token，来解决这个race condition。 帮我设计并实现。token过期时间一般为2小时，refresh token过期时间为一个月我把刷新的业务逻辑代码放在原来的hornbill_outsourcing_api中好，还是放在新的FaaS中但是这样代码没法复用了，还得在hertz上重新搭建一套框架直接基于现有FaaS伤的Native Hertz框架，生成代码

[Expected Artifacts]
Code_Repository: 基于用户指定的Native Hertz框架生成一个完整的、可运行的代码项目。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 你需要全面分析多个代码仓库的结构和业务逻辑，为后续开发奠定基础。具体任务包括：

1. **分析目标代码仓库** (inspection_optimize)：
   - 理解其 FaaS 架构规范和代码组织方式
   - 分析现有的代码结构、模块划分
   - 了解项目的技术栈和开发规范

2. **分析参考代码仓库**：
   - stability_inspection (FaaS)：重点分析其代码逻辑和目录架构
   - detector_core (RPC)：理解 RPC 接口设计
   - stability_inspection_api (API)：了解 API 接口规范

3. **理解业务逻辑**：
   - 阅读第一期开发逻辑文档 (优化任务平台开发.lark.md)
   - 理解巡检项管理的业务流程
   - 分析 openapi 调用的具体需求

4. **技术调研**：
   - 分析各仓库间的调用关系
   - 理解权限鉴权机制
   - 了解异常处理模式

请生成一份详细的分析报告，包括：
- 各仓库的架构分析
- 业务流程理解
- 技术实现要点
- 为后续开发提供的建议和规范

注意：如果遇到权限限制无法访问的内容，请明确说明。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
首先，我将分析用户的自然语言输入，提取关键信息，以确定所需的数据模型、指标、维度和筛选条件。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 Your task is to create a final evaluation report in the specified Lark Sheet format by combining the evaluation data with a specific template.

**Execution Plan:**

1.  **Analyze Template**: Download and analyze the structure of the Lark Sheet template from the URL: `https://bytedance.larkoffice.com/sheets/U1T4sPVkZhp50GtRBCfcIktVngp`. Understand its structure, including sheet names, column headers, and expected data format.
2.  **Load Data**: Read the evaluation data from the `evaluation_results.json` file and the list of failed item IDs from the `failed_tids.txt` file.
3.  **Create Report**:
    *   Create a new Lark Sheet.
    *   Based on the template's structure, populate the new sheet with the data from `evaluation_results.json`. This will require creating two main sections/sheets as per the template: one for the ""Evaluation Conclusion"" and one for the ""Evaluation Details"".
    *   **Strict Formatting**: Ensure the column headers, data order, and overall structure in your new document **exactly match** the template.
    *   **Include Failures**: Add a note or a separate small table in the report to list the 18 item IDs from `failed_tids.txt` that could not be processed.
4.  **Finalize**: Provide the link to the newly created Lark Sheet as the final output.

**Important Notes:**
- The previous attempt failed due to a temporary authentication error. Please retry the process.
- The final report's format must be a strict replica of the provided template.
- The final deliverable is the URL of the new Lark Sheet.
- You have access to the files `evaluation_results.json` and `failed_tids.txt` from the previous step. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始代码分析，在 tikcast/cohost_api 中搜索和定位 /webcast/linkmic/rivals 接口的实现
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 你的任务是将一个CSV文件的数据转换并上传到一个新的飞书表格中。

**背景**: 在上一个步骤中，来自多个源的数据已经被成功整合到了 `final_consolidated_data.csv` 文件中。现在需要将这些数据呈现为最终的飞书表格。

**具体目标**:
1.  **读取数据**: 读取 `final_consolidated_data.csv` 文件的内容。
2.  **创建Lark表格**:
    -   使用 `lark_creation` 工具的 `convert_file_to_lark_sheet` 功能。
    -   将 `final_consolidated_data.csv` 文件作为输入。
    -   为新创建的飞书表格命名，例如：“最终整合数据报告”。

**重要提示**:
-   确保上传过程完整无误，新的飞书表格应包含CSV文件中的所有数据和列。
-   最终生成的飞书表格链接将是本次任务的最终交付成果。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
完成项目总结和最终验证，确保所有功能都能正常工作
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
基于数据分析结果设计综合评分算法，考虑历史最高消费等级权重、音乐评级权重、头部数量和占比分析
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
基于已收集的代码信息，开始进行五个维度的详细分析，并准备生成架构分析报告
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>

## Objective:
    将Markdown格式的测试清单草稿转换为一个交互式的飞书文档，其中所有测试项均为可勾选的Checklist格式。

## Actions(You should adapt the actions to the current task):
    1.  **读取草稿**：读取 `test_checklist.md` 文件的内容。
    2.  **创建飞书文档**：
        *   调用 `lark_creation` 工具。
        *   设置一个合适的文档标题，例如：“{项目名称}研发自测清单”。
        *   将Markdown内容转换为飞书文档格式，并确保所有测试项都呈现为可勾选的待办事项（Checklist）。
    3.  **验证输出**：确认生成的飞书文档链接有效，且内容和格式均符合要求。

## Deliverable:
- [研发自测清单飞书文档] (suixintuan_checklist.lark.md)

</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始整合信息并创建知识摘要文件。首先，创建 `minix_stability_summary.md` 文件，并写入第一部分“Minix系统概述”的内容。这部分内容将基于已阅读的技术方案文档。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 You are a document specialist. Your task is to convert a markdown file into a polished Lark document.

1.  **Input File:** Use the `technical_solution_draft.md` file, which contains the complete content for the technical solution.
2.  **Action:** Convert this markdown file into a new Lark document.
3.  **Title:** The title of the new Lark document should be ""【技术方案】语音虚拟全场景&宫格30w-公会流水任务调整"".

**Final Output:**
A new Lark document containing the complete technical solution. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始第一阶段，深入分析与比较两个文档。我将首先从核心目标与受众入手，然后逐步完成内容、方法、关键信息等方面的对比。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已完成，准备总结成果
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 你的任务是为制作“植物大战僵尸”识字卡项目进行初步的研究和信息收集。请完成以下三个目标：

1.  **搜集完整的植物名录**:
    *   查找并整理一份涵盖《植物大战僵尸》所有版本（包括但不限于原版、长城版、西游版、2代、英雄等）的植物完整列表。
    *   确保植物名称为官方中文译名，并验证其准确性。

2.  **寻找图片素材来源**:
    *   搜索并推荐几个可以提供高质量、官方或社区创作的植物图片的网站或资源库。
    *   在推荐时，请特别注意版权问题，优先考虑粉丝维基(Fandom Wiki)、官方网站或明确授权的资源。

3.  **研究图片生成提示词 (Prompt)**:
    *   研究并提供一些关于如何编写有效提示词（Prompt）的通用指南和具体示例，以便使用AI图像生成工具（如Midjourney, Stable Diffusion等）创作出风格统一、形象可爱的植物大战僵尸角色图片。可以参考附件中的“豌豆射手.jpeg”作为风格参考。

请将你的研究结果整理成一份清晰的报告。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_9"">
<title>progress example - general</title>
<use_this_knowledge_when>
it should always be used for better planning.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 ### 任务：拉取OnCall数据

请使用 `oncall` 工具完成以下任务：

1.  **数据源**: 基于用户提供的URL `https://cloud.bytedance.net/oncall/chats/user/userCase?id=79732770820...` 作为参考。
2.  **拉取条件**:
    *   租户ID (`tenant_id`): `4112`
    *   开始时间 (`start_time`): `2025-08-24`
    *   结束时间 (`end_time`): `2025-08-31`
3.  **输出格式**: 将拉取到的原始数据保存为名为 `oncall_raw_data.xlsx` 的Excel文件。

**重要提示**:
*   请务必确保拉取操作严格遵循上述租户ID和时间范围。
*   输出的文件必须是 `.xlsx` 格式，以供后续处理步骤使用。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 分析以下6个GitHub项目，根据指定标准筛选出相关度最高的3个项目，并生成详细的飞书文档报告。

**项目列表：**
1. https://github.com/Fosowl/agenticSeek (已下载到agenticSeek目录)
2. https://github.com/GoogleCloudPlatform/kubectl-ai (已下载到kubectl-ai目录)
3. https://github.com/Lightricks/LTX-Video (已下载到LTX-Video目录)
4. https://github.com/bytedance/flowgram.ai (需要获取信息)
5. https://github.com/mem0ai/mem0 (已下载到mem0目录)
6. https://github.com/mindsdb/mindsdb (已下载到mindsdb目录)

**分析标准：**
1. 是否是后端项目
2. 主要编程语言是否为Go/Python/Java
3. 是否与K8s或LLM相关

**优先级：** 后端项目 > 主要编程语言相符 > 与K8s或LLM相关
（并列情况下按项目名字母排序取前三）

**任务要求：**
1. 分析每个项目的代码结构、README、配置文件等，判断是否符合上述三个标准
2. 根据优先级规则筛选出相关度最高的3个项目
3. 对筛选出的3个项目提供详细介绍，包括：
   - 项目介绍
   - 主要开发人员
   - 所属公司（如果有）
   - 技术栈和架构
   - 与K8s/LLM的关联性

**最终输出：** 生成一份结构化的飞书文档，包含分析过程和详细的项目介绍。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 基于用户提供的详细对话存储结构示例，你需要设计精简高效的短期记忆和长期记忆存储结构。任务包括：

1. **现有结构分析**：
   - 分析提供的Message结构，识别核心信息和冗余数据
   - 理解对话的关键要素：用户查询、Agent响应、工具调用、时间信息等
   - 分析哪些信息对记忆管理最有价值

2. **记忆需求分析**：
   - 短期记忆特点：保留最近的对话上下文，支持连续对话
   - 长期记忆特点：保留用户偏好、行为模式、重要历史信息
   - 存储容量和性能考虑

3. **存储结构设计**：
   - 设计短期记忆存储结构（保留必要的对话上下文信息）
   - 设计长期记忆存储结构（保留用户画像和重要历史信息）
   - 确保结构简洁但信息完整，便于检索和使用

4. **字段说明和策略**：
   - 为每个字段提供详细说明和使用场景
   - 设计存储策略（何时存储、如何更新、何时清理）
   - 提供不同场景下的使用示例

5. **文档准备**：
   - 准备完整的设计说明和最佳实践
   - 创建实施建议和注意事项
   - 设计与现有系统的集成方案

请将所有内容整理成结构化的文档格式，为后续生成Lark文档做准备。重点关注存储结构的实用性和效率。

提供的对话结构示例包含了丰富的信息，包括消息ID、用户查询、Agent回答、工具调用、用户信息等，你需要从中提取最有价值的信息来设计记忆存储结构。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""data_analyze.2"">
<title>LLM Guide for Data Analysis and Classification</title>
<use_this_knowledge_when>
Process text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_18_aeolus"">
<title>aeolus tips</title>
<use_this_knowledge_when>
When the task is related to aeolus(风神 fengshen) data. You MUST use this knowledge.
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_1"">
<title>data analysis working principles</title>
<use_this_knowledge_when>
when processing or analyzing data (including git history, maps, tables, etc.)
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_1_newbie"">
<title>data analysis working principles</title>
<use_this_knowledge_when>
when processing or analyzing data (including git history, maps, tables, etc.)
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_25_2"">
<title>guide for preparing content for lark templates reports</title>
<use_this_knowledge_when>
use it whenever/wherever preparing content for a lark template or markdown report, analyzing data or generating visualization part for reports, even if the current task is just creating intermediate artifacts or doing analysis that will later be integrated into a final document.

</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_3"">
<title>table data processing principles</title>
<use_this_knowledge_when>
when need process/filter/generate excel/csv data files
</use_this_knowledge_when>
</item>

<item id=""data_analyze.dynamic_3_newbie"">
<title>excel processing principles</title>
<use_this_knowledge_when>
when processing or generating excel files, especially when doing data analysis
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 You are a senior quality assurance engineer specializing in data-driven test optimization. Your task is to create a Python script to process regression testing data for an XGBoost model. The process involves three main stages: feature generation, test unit labeling, and story labeling.

**Input Files (available in the workspace):**
1.  `raw_case_ic.json`: The baseline regression case set for feature generation.
2.  `ic_unit_keys.json`: The list of all test units that need to be labeled.
3.  `ic_stories.json`: The list of new stories that need to be labeled.

**Your script must perform the following actions in sequence:**

**Part 1: Feature Generation**
1.  **Analyze `raw_case_ic.json`:** Read this large file and establish a hierarchical feature set based on its content. The user suggests that features based on ""module"" (一级特征) and ""sub_module"" (二级特征) have proven effective. You need to devise a strategy (e.g., using NLP techniques like topic modeling, keyword extraction, or clustering) to group the cases and define these modules.
2.  **Adhere to Constraints:**
    *   The total number of unique features (labels) must not exceed 200.
    *   The number of top-level `module` features must be between 10 and 20.
3.  **Output `labels.json`:** Save the generated features in this file.
    *   **Format:** A list of dictionaries: `[{""id"": 0, ""sub_module"": ""sub_module_A"", ""module"": ""module_1""}, ...]`
    *   The `id` must be a unique, continuous integer for each feature.

**Part 2: Test Unit Labeling**
1.  **Process `ic_unit_keys.json`:** Read the list of test units.
2.  **Assign Labels:** For each unit, assign the most appropriate feature/label from the `labels.json` you created. You will need to develop a robust mapping or similarity matching strategy.
3.  **Constraint:** Every unit must be assigned at least one label.
4.  **Output `labeled_case_unit_key.json`:** Save the results in this file.
    *   **Format:** A dictionary where the unit name is the key and the label dictionary is the value: `{""unit_01"": {""id"": 0, ""sub_module"": ""XX"", ""module"": ""XX""}, ...}`

**Part 3: Story Labeling**
1.  **Process `ic_stories.json`:** Read the list of stories.
2.  **Assign Labels:** For each story, use its title (`story_name`) and other available information to assign exactly one feature/label from `labels.json`.
3.  **Constraint:** Each story must have one and only one label.
4.  **Output `labeled_stories.json`:** Save the results in this file.
    *   **Format:** A list of dictionaries: `[{""story_name"": ""s1"", ""labels"": [{""id"": 0, ""sub_module"": ""xx"", ""module"": ""xx""}]}, ...]`

**Important Notes:**
*   **Large Files:** The input files are very large. You MUST use efficient data handling strategies like batching or streaming to avoid memory issues.
*   **Mapping Logic:** Clearly define and implement your logic for mapping features to units and stories. This is a critical part of the task.
*   **Data Integrity:** Before finalizing the output files, validate that all constraints are met (e.g., all units are labeled, stories have exactly one label, IDs are unique and continuous).
*   **Code:** Save the final, complete Python script used for this entire process in the workspace.

Your final deliverable is the set of three JSON files and the Python script that generated them. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 基于之前的全面搜索分析，你已经完成了多种搜索策略但未找到直接的 TCC 接口调用。现在需要深入分析搜索结果并生成完整的报告：

**最终分析任务：**

1. **深入检查语义搜索结果：**
   - 详细检查语义搜索识别出的关键文件，特别是：
     - `util/echat_common/request.go` (可能包含 HTTP 请求逻辑)
     - `biz/infra/rpc/im_conversation.go` (可能包含 RPC 调用)
     - `biz/service/clue_service_order_service.go` (可能包含服务调用)
     - `biz/infra/clients/init.go` (可能包含客户端初始化)
   - 查看这些文件是否包含间接的 TCC 接口调用或相关配置

2. **检查 HTTP 客户端和配置：**
   - 查看 HTTP 客户端配置和初始化代码
   - 检查是否有通过配置文件或环境变量动态构建的 API 调用
   - 分析 TCC 配置如何被使用

3. **生成完整的飞书文档报告：**
   创建一份专业的分析报告，包含以下内容：
   - **执行摘要**：搜索过程和主要发现
   - **项目概述**：代码库结构和技术栈
   - **搜索方法**：使用的搜索策略和工具
   - **TCC 相关发现**：找到的 TCC 相关文件和配置
   - **接口调用分析**：
     - 直接搜索结果（未找到指定接口）
     - 间接调用可能性分析
     - 相关代码片段（如果有）
   - **结论和建议**：
     - 是否存在指定的 TCC 接口调用
     - 代码合规性评估
     - 后续建议

**重要要求：**
- 即使未找到直接匹配，也要提供完整的分析过程和结论
- 包含具体的代码片段和文件路径作为证据
- 提供专业的技术分析和建议
- 确保报告结构清晰，便于审查和决策 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
任务已完成，准备总结成果并提供最终结论
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
进行全面的批量搜索，收集关于Scikit-learn Moons数据集、视频few-step生成、合成数据在深度学习中的应用等相关信息
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""planner.planner_18"">
<title>Planning Example</title>
<use_this_knowledge_when>
always
</use_this_knowledge_when>
</item>

<item id=""planner.planner_1"">
<title>rich format file convert tips</title>
<use_this_knowledge_when>
when user provide rich format files(such as pdf, microsoft office files, etc.) and need to understand. or need to convert file format.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_21"">
<title>Text-to-Speech (TTS) capability knowledge</title>
<use_this_knowledge_when>
You MUST use this knowledge when user requests text-to-speech conversion.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_23"">
<title>Word Document Processing Capability</title>
<use_this_knowledge_when>
When user asks to read, extract, or convert Word documents
</use_this_knowledge_when>
</item>

<item id=""planner.planner_24"">
<title>Batch Processing Capability for Mewtwo</title>
<use_this_knowledge_when>
When tasks involve repetitive operations that can benefit from batch processing - multiple similar operations on different inputs.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_25"">
<title>Data Visualization</title>
<use_this_knowledge_when>
the user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_12"">
<title>Plan guide for tasks operations on specific website</title>
<use_this_knowledge_when>
when user query contains task that is related to downloading data from specific website, exporting data, etc.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
针对产品 `chatgpt deep research,扣子空间` 生成一份深入的对比分析报告

1. 报告应全面呈现 `chatgpt deep research,扣子空间` 的核心定位、技术特点、市场表现、用户反馈以及发展规划。
2. 报告需对比分析 `chatgpt deep research,扣子空间` 与同类竞品（如果适用），并深入剖析当前行业格局、未来技术趋势及潜在商业机会。
### 格式与输出要求：
1. 飞书文档：
- 标题： `chatgpt deep research,扣子空间` 深度调研报告
- 包含所有结构化内容，并使用清晰的标题层级、列表和表格等专业排版格式。
- 交付： 生成可直接上传至飞书的内容。
最终报告应结构清晰、分析深刻、结论明确，旨在提供对 `chatgpt deep research,扣子空间` 的全面理解和战略洞察。

[Expected Artifacts]
Feishu/Lark文档: 用于撰写和呈现关于 `chatgpt deep research` 与 `扣子空间` 的深度对比分析报告，报告将包含所有研究内容、分析和结论，并按照用户要求的专业排版格式进行组织。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 You need to conduct a comprehensive OnCall analysis and generate an AI efficiency improvement report. The task involves multiple interconnected components that should be completed as a unified analysis:

**Background Context:**
- You have access to a Feishu document containing 300+ OnCall records with OnCall detail URLs
- Reference documents are available in the workspace for AI capability framework and analysis examples
- The goal is to create an AI-powered OnCall efficiency improvement strategy

**Main Objectives:**
1. **Data Analysis**: Read and analyze the OnCall records from the provided Feishu document
2. **Batch OnCall Analysis**: Process each OnCall detail URL to extract problem manifestations and solutions using batch processing capabilities
3. **Problem Clustering**: Categorize and cluster the identified problems based on patterns and similarities
4. **Visualization**: Create charts showing problem distribution and clustering results for embedding in the final document
5. **AI Capability Recommendations**: Based on the reference documents, propose specific AI business capabilities needed to automatically resolve these clustered problems
6. **Final Report**: Generate a comprehensive Feishu document containing all analysis results, visualizations, and recommendations

**Key Requirements:**
- Process all 300+ records efficiently using batch operations
- Follow the analysis approach shown in the reference document about 特效不可用/拍同款 problems
- Generate visualizations that clearly show problem clustering and distribution
- Provide concrete AI capability suggestions based on the AI x OnCall framework document
- Create a well-structured Feishu document as the final deliverable

**Available Resources:**
- OnCall records document (already downloaded)
- AI framework reference document: 素材生态_-_AI_x_Oncall提效专项Onepage.lark.md
- Analysis example document: AI_oncall提效建设记录（持续更新）.lark.md

The final output should be a comprehensive Feishu document that serves as a strategic guide for OnCall AI efficiency improvements. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 针对用户提供的宠物医疗决策困境，进行全面的研究分析并生成一份详细的飞书/Lark决策分析报告。

背景信息：
- 阿福：12岁老犬，后肢行走困难，早期肾病（血肌酐高）
- 三个治疗方案：①关节置换手术（3.5万+后续费用）②保守治疗（药物+辅助工具）③安乐死
- 家庭经济状况：月收入2.2万，房贷7000，存款8万

你需要完成以下研究和分析：

1. **医学研究分析**：
   - 老年犬关节置换手术的成功率、并发症概率、术后生活质量
   - 肾病对麻醉和手术的风险影响
   - 保守治疗（关节封闭针、护肾药）的有效性和局限性
   - 宠物轮椅的实际使用效果和潜在问题
   - 肾病进展速度和恶化概率

2. **经济成本分析**：
   - 三个方案的总成本对比（包括隐性成本）
   - 家庭财务承受能力评估
   - 潜在额外费用风险

3. **生活质量评估**：
   - 每种方案对阿福生活质量的影响
   - 疼痛管理效果对比
   - 预期寿命和活动能力

4. **决策框架建议**：
   - 提供结构化的决策思路
   - 风险权衡分析
   - 折中方案探讨
   - 后续行动指南

最终交付物应该是一份结构完整的飞书/Lark文档，包含详细分析、数据支撑、决策建议和实用指导，帮助用户做出明智的决定。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
首先深入分析 VideoThumbnailLoader 的缓存实现，包括 LightStorage 缓存系统和 VideoThumbnailPathCache 的具体实现细节
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 **🚨 MANDATORY FIRST**: Your first action MUST be to use the read_file tool on the template original document ""头条交付效率&需求收益分析_-_【本周期】.lark.md"" and the analysis document ""模板分析与数据源策略.md"". Do not proceed to any other actions until this is complete.

After reading the template and strategy documents, complete the data processing and content generation for Section 2 (资源投入分析):

**Section 2 Tasks:**
1. **Data Processing for 资源投入分析**:
   - Process the human resource and workload data from the downloaded sources
   - Analyze resource allocation across different business lines
   - Calculate investment ratios, resource distribution percentages
   - Identify top resource-consuming areas and trends

2. **Excel File Generation**:
   - Fill the second Excel template file (0IV4my.xlsx) with processed resource investment data
   - Include detailed breakdown of resource allocation by business line
   - Apply proper formatting consistent with template requirements

3. **Chart Generation**:
   - Create pie charts for resource investment distribution as specified in the template
   - Generate charts showing overall investment distribution and business line-specific breakdowns
   - Create visualization for resource allocation trends and patterns
   - Save all charts as individual image files for document embedding

4. **Content Generation**:
   - Fill Section 2 content in the template format
   - Include analysis of resource allocation efficiency
   - Provide insights on investment patterns and recommendations
   - Ensure content aligns with template structure and business requirements

**Important Notes:**
- Build upon the data processing foundation established in Section 1
- Use the business line mapping logic consistently
- Focus on resource allocation analysis and investment distribution
- Maintain data accuracy and validate all calculations
- Generate pie charts as indicated in the Historical Experience for this template
- Save all intermediate results and generated files for reference

The output should be a completed Section 2 with all resource data processed, Excel files filled, pie charts generated, and content written according to the template requirements. 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""planner.planner_18"">
<title>Planning Example</title>
<use_this_knowledge_when>
always
</use_this_knowledge_when>
</item>

<item id=""planner.planner_1"">
<title>rich format file convert tips</title>
<use_this_knowledge_when>
when user provide rich format files(such as pdf, microsoft office files, etc.) and need to understand. or need to convert file format.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_21"">
<title>Text-to-Speech (TTS) capability knowledge</title>
<use_this_knowledge_when>
You MUST use this knowledge when user requests text-to-speech conversion.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_23"">
<title>Word Document Processing Capability</title>
<use_this_knowledge_when>
When user asks to read, extract, or convert Word documents
</use_this_knowledge_when>
</item>

<item id=""planner.planner_24"">
<title>Batch Processing Capability for Mewtwo</title>
<use_this_knowledge_when>
When tasks involve repetitive operations that can benefit from batch processing - multiple similar operations on different inputs.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_25"">
<title>Data Visualization</title>
<use_this_knowledge_when>
the user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_12"">
<title>Plan guide for tasks operations on specific website</title>
<use_this_knowledge_when>
when user query contains task that is related to downloading data from specific website, exporting data, etc.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
CapCut仓库：******************:faceu-ios/CapCut.git
Tiktok仓库：******************:ugc/TikTok.git

我们CapCut在编辑器内用了一些功能之后，导出，然后分享到Tiktok，最终会在tiktok的视频上挂载一个锚点，其他用户如果看到这个锚点，点击，我们又会在端上去做一系列的承接，请你仔细分析这条链路，输出一份详尽的报告
你分析完实现之后，请你挖掘更多的细节，整理一篇帮助其他同学排障和接入的One Pager
这个还是太技术范了，请你写一个面向QA人员的
请你参考这个文档https://bytedance.larkoffice.com/docx/BFyDdyKaFoyMncxsglHc1jytnEc，根据现有代码，补充文档里的更多细节
你增强版的文档在哪里？我没有看到
请你更加详细的分析双端锚点链路的逻辑，不要告诉我代码逻辑，而是要从里面读出业务逻辑是什么样子的
补充一下细节，例如：
1. 抖音锚点展示逻辑里面，分别读取了metadata中的哪些信息，找到关键代码，并详细描述
2. 剪映的代码里面metadata写入了有什么，是在什么时机写入的，如果想要新增key应该怎么办？


[Expected Artifacts]
Feishu/Lark文档: 用于撰写一份详细的代码分析报告，内容将包括对抖音和剪映metadata处理逻辑的深入解释、关键代码片段展示以及如何进行修改的指导。
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
开始模块化分析，首先使用语义搜索深入了解各个功能模块的具体实现和职责
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego tool principle</title>
<use_this_knowledge_when>
when working with meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_4_html_citation"">
<title>HTML Citation and Reference Management</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 进行深入的技术研究，收集关于大模型优化工具实际应用和配合使用的详细信息。你的任务包括以下几个方面：

1. **实际案例收集**：
   - 搜索模型层工具（如PyTorch Profiler、GPTQ、LM Eval Harness等）发现和解决实际问题的具体案例
   - 收集系统层工具（如Nsight Systems、vLLM Profiler、Prometheus等）在大模型优化中的实际应用案例
   - 分析工具如何定位瓶颈、提供解决方案的完整流程

2. **工具配合策略研究**：
   - 研究模型层与系统层工具的协同工作流程
   - 分析不同优化阶段应该使用哪些工具组合
   - 收集工具链配合的最佳实践和方法论

3. **硬件级工具集成方案**：
   - 研究Nsight Compute在大模型优化中的具体应用和配合方案
   - 分析AMD OmniTrace和OmniPerf与其他工具的集成使用
   - 收集跨平台性能分析的实践经验

4. **业界最佳实践**：
   - 收集大厂（如OpenAI、Meta、Google等）在大模型优化中的工具使用经验
   - 分析成功的优化案例和失败的教训
   - 研究工具选择和评估的标准化方法

5. **技术深度分析**：
   - 分析不同工具的技术原理和适用场景
   - 研究工具间数据传递和结果关联的机制
   - 收集性能优化的量化指标和评估方法

请将收集到的信息整理成结构化的研究摘要，重点突出实际案例和工具配合的具体操作流程，为后续生成详细报告做准备。 
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
完成最终验证，提供合并请求链接和完整的任务完成总结
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""planner.planner_1"">
<title>rich format file convert tips</title>
<use_this_knowledge_when>
when user provide rich format files(such as pdf, microsoft office files, etc.) and need to understand. or need to convert file format.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_21"">
<title>Text-to-Speech (TTS) capability knowledge</title>
<use_this_knowledge_when>
You MUST use this knowledge when user requests text-to-speech conversion.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_23"">
<title>Word Document Processing Capability</title>
<use_this_knowledge_when>
When user asks to read, extract, or convert Word documents
</use_this_knowledge_when>
</item>

<item id=""planner.planner_24"">
<title>Batch Processing Capability for Mewtwo</title>
<use_this_knowledge_when>
When tasks involve repetitive operations that can benefit from batch processing - multiple similar operations on different inputs.
</use_this_knowledge_when>
</item>

<item id=""planner.planner_25"">
<title>Data Visualization</title>
<use_this_knowledge_when>
the user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_12"">
<title>Plan guide for tasks operations on specific website</title>
<use_this_knowledge_when>
when user query contains task that is related to downloading data from specific website, exporting data, etc.
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
 @[apaas/rule](https://code.byted.org/apaas/rule) 
https://bytedance.larkoffice.com/docx/GkBRdBBYnoYuAfxq8Idc9yt1nco
识别这个文档，并输出一份关于rule的详细文档，可以基于criterionBatchEval方法来说，最好画一些流程图
解释下filter公式是如何运转的，如果右值是filter公式，他是如何过滤数据的，在哪个时间段去过滤数据的，是将表达式转成oql发给metadata让他们过滤，还是查出所有的记录在内存里过滤的
 @[apaas/rule](https://code.byted.org/apaas/rule) 
https://bytedance.larkoffice.com/wiki/wikcnxCBTmdQTZv6B4zYtnjBS6e
不可集成的filter直接返回SubQueryExpression，这个相当于是一个新的criterion，一般criterion不是返回true/false吗，他是怎么返回数据的
 @[apaas/rule](https://code.byted.org/apaas/rule) 
 @[apaas/rule](https://code.byted.org/apaas/rule) 
再详细的给出criterion2oql接口的实现逻辑


[Expected Artifacts]
Lark/Feishu Document: 用于详细分析SubQueryExpression的实现机制，解释criterion的返回逻辑，以及filter的工作原理
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"
"<system>
Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return ""no useful knowledge items"".
Note: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.

# Available knowledge items you can choose from:

<item id=""system.coder_4"">
<title>Collaboration and Version Control Best Practices</title>
<use_this_knowledge_when>
when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_14_2"">
<title>validate generated images</title>
<use_this_knowledge_when>
when generated images, to check the rendered content as expected, no errors, no rendering issues.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_27"">
<title>PDF file creation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge if you need create a PDF file.
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_36"">
<title>Project Structure and Code Organization</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""system.dynamic_8"">
<title>meego principle</title>
<use_this_knowledge_when>
when task is about meego
</use_this_knowledge_when>
</item>

<item id=""mewtwo.15"">
<title>time conversion tips</title>
<use_this_knowledge_when>
Use this when involving time processing.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.17"">
<title>PDF Processing Guidelines</title>
<use_this_knowledge_when>
working with PDF documents that require efficient reading and information extraction.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.19"">
<title>python json save/parse tips</title>
<use_this_knowledge_when>
when using python json lib to save or parse json data.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.3"">
<title>ECharts Tips</title>
<use_this_knowledge_when>
when using echarts
</use_this_knowledge_when>
</item>

<item id=""mewtwo.5"">
<title>display maps in web browser tips</title>
<use_this_knowledge_when>
when display or draw maps in web browser/frontend site.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.6"">
<title>python pandas lib tips</title>
<use_this_knowledge_when>
when using python pandas lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.7"">
<title>python plotly lib tips</title>
<use_this_knowledge_when>
when using python plotly lib.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_1"">
<title>Coding Best Practices</title>
<use_this_knowledge_when>
when writing code in any programming language
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_3"">
<title>Testing and Debugging Best Practices</title>
<use_this_knowledge_when>
when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_7"">
<title>Media Guidelines</title>
<use_this_knowledge_when>
when creating documents, presentations, or website
</use_this_knowledge_when>
</item>

<item id=""mewtwo.coder_8"">
<title>general html generation principles</title>
<use_this_knowledge_when>
when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_16"">
<title>slardar bug fix standard</title>
<use_this_knowledge_when>
When the user mentions Slardar scenarios
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_18"">
<title>Loop Tool Execution for Efficiency</title>
<use_this_knowledge_when>
Execute batch tool calling using loop tools to save time and reduce manual effort.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_28"">
<title>Text-to-Speech (TTS) implementation guidelines</title>
<use_this_knowledge_when>
You MUST use this knowledge when implementing text-to-speech conversion tasks.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_31"">
<title>pandoc document conversion tool</title>
<use_this_knowledge_when>
task involves converting Word documents to markdown format
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_33"">
<title>Document Writing Guidelines</title>
<use_this_knowledge_when>
task involves writing any document, including but not limited to research report, technical design, news, blog, presentation, marketing materials, manuscript, and other textual materials.
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_37"">
<title>PlantUML guidelines and restrictions</title>
<use_this_knowledge_when>
when need to draw PlantUML diagrams
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_38_chart_with_python"">
<title>data visualization with Python preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_39_data_visualization"">
<title>data visualization preferences</title>
<use_this_knowledge_when>
visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
</use_this_knowledge_when>
</item>

<item id=""mewtwo.dynamic_40"">
<title>Image Search or Use Guidelines</title>
<use_this_knowledge_when>
when using image_search tool or using images from image_search_result/ directory or needing to get image information from image_search_result/ directory
</use_this_knowledge_when>
</item>

<item id=""mewtwo.planner_16"">
<title>mermaid guidelines and tips</title>
<use_this_knowledge_when>
when need to draw mermaid diagrams
</use_this_knowledge_when>
</item>

<item id=""system.planner_11"">
<title>diagrams</title>
<use_this_knowledge_when>
User's task may require drawing diagrams
</use_this_knowledge_when>
</item>


Output format:
<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>
<id>1</id>

<rationale>short rationale(one sentence) why to choose knowledge id 20</rationale>
<id>20</id>

</system>
<user>
User's task:
<task>
使用图片搜索工具根据改写的query进行图片搜索，筛选符合""白色背景、无模特、无品牌、无包装""条件的图片
</task>

Walk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.

- If the knowledge's used_when is relevant or potentially useful, choose it.
- If the knowledge's used_when is never relevant, skip it and do not write rationale. Only selected knowledge items should be written.
- Even for simple tasks, knowledge may be useful for handling unexpected situations.
- Each selected item should be written in a standalone line, in `<id>xxx</id>` format.

DO NOT skip reviewing any single knowledge item, review one by one.
</user>"