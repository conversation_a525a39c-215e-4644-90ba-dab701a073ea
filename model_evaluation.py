#!/usr/bin/env python3
"""
Model Evaluation Script: Gemini-2.5 Flash Reasoning vs Close Reasoning
Evaluates model performance against Claude ground truth for knowledge item selection.
"""

import json
import csv
import time
import re
import requests
import uuid
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ModelResult:
    """Store model evaluation results"""
    knowledge_ids: Set[str]
    response_time: float
    raw_response: str

@dataclass
class CaseEvaluation:
    """Store evaluation results for a single case"""
    case_index: int
    case_content: str
    ground_truth_ids: Set[str]
    ground_truth_count_range: Tuple[int, int]
    ground_truth_time_range: Tuple[float, float]
    ground_truth_avg_time: float
    ground_truth_content: str

    gemini_reasoning_ids: Set[str]
    gemini_reasoning_valid: bool
    gemini_reasoning_count_range: Tuple[int, int]
    gemini_reasoning_time_range: Tuple[float, float]
    gemini_reasoning_avg_time: float
    gemini_reasoning_content: str

    # New: Gemini reasoning small
    gemini_reasoning_small_ids: Set[str]
    gemini_reasoning_small_valid: bool
    gemini_reasoning_small_count_range: Tuple[int, int]
    gemini_reasoning_small_time_range: Tuple[float, float]
    gemini_reasoning_small_avg_time: float
    gemini_reasoning_small_content: str

    gemini_close_ids: Set[str]
    gemini_close_valid: bool
    gemini_close_count_range: Tuple[int, int]
    gemini_close_time_range: Tuple[float, float]
    gemini_close_avg_time: float
    gemini_close_content: str

    # New: GPT-5
    gpt5_ids: Set[str]
    gpt5_valid: bool
    gpt5_count_range: Tuple[int, int]
    gpt5_time_range: Tuple[float, float]
    gpt5_avg_time: float
    gpt5_content: str

class ModelEvaluator:
    """Main evaluation class"""

    def __init__(self):
        self.gemini_ak = "L0m34Ncl68uymsVan1Pk9VrIbqW1VXuE_GPT_AK"
        self.claude_ak = "QlqvAeHFYSuqzoQSH3kuxzl2N5anzDUb_GPT_AK"
        self.gemini_url = "https://search.bytedance.net/gpt/openapi/online/multimodal/crawl"
        self.claude_url = "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"

    def generate_logid(self) -> str:
        """Generate unique log ID"""
        return str(uuid.uuid4())

    def extract_knowledge_ids(self, response: str) -> Set[str]:
        """Extract knowledge IDs from model response"""
        pattern = r'<id>([^<]+)</id>'
        matches = re.findall(pattern, response, re.IGNORECASE)
        return set(matches)

    def call_claude(self, messages: List[Dict]) -> ModelResult:
        """Call Claude model"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        # Convert messages format for Claude
        claude_messages = []
        for msg in messages:
            claude_messages.append({
                "role": msg['role'],
                "content": [{
                    "type": "text",
                    "text": msg['content']
                }]
            })

        payload = {
            "stream": False,
            "messages": claude_messages,
            "model": "aws_sdk_claude37_sonnet",
            "max_tokens": 1024,
            "Temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.claude_url}?ak={self.claude_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Claude API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gemini_reasoning(self, messages: List[Dict]) -> ModelResult:
        """Call Gemini with reasoning (default)"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        payload = {
            "stream": False,
            "model": "gemini-2.5-flash",
            "max_tokens": 4096,
            "messages": messages,
            "thinking": {
                "include_thoughts": True,
                "budget_tokens": 2048
            },
            "temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.gemini_url}?ak={self.gemini_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Gemini reasoning API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gemini_reasoning_small(self, messages: List[Dict]) -> ModelResult:
        """Call Gemini with reasoning (500 tokens)"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        payload = {
            "stream": False,
            "model": "gemini-2.5-flash",
            "max_tokens": 2048,
            "messages": messages,
            "thinking": {
                "include_thoughts": True,
                "budget_tokens": 512
            },
            "temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.gemini_url}?ak={self.gemini_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Gemini reasoning API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gemini_close(self, messages: List[Dict]) -> ModelResult:
        """Call Gemini without reasoning"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        payload = {
            "stream": False,
            "model": "gemini-2.5-flash",
            "max_tokens": 2048,
            "messages": messages,
            "thinking": {
                "include_thoughts": False,
                "budget_tokens": 0
            },
            "temperature": 0.2
        }

        try:
            response = requests.post(
                f"{self.gemini_url}?ak={self.gemini_ak}",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"Gemini close API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_gpt5(self, messages: List[Dict]) -> ModelResult:
        """Call GPT-5 model"""
        start_time = time.time()

        headers = {
            'Content-Type': 'application/json',
            'X-TT-LOGID': self.generate_logid()
        }

        # Convert messages format for GPT-5 (similar to Claude format)
        gpt5_messages = []
        for msg in messages:
            gpt5_messages.append({
                "role": msg['role'],
                "content": [{
                    "type": "text",
                    "text": msg['content']
                }]
            })

        payload = {
            "stream": False,
            "model": "gpt-5-2025-08-07",
            "max_tokens": 2048,
            "messages": gpt5_messages,
            "temperature": 1
        }

        try:
            response = requests.post(
                "https://search.bytedance.net/gpt/openapi/online/v2/crawl?ak=GDlcrsO2neW02VNGGVV5Bk5TUWgtABeP",
                headers=headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()

            response_time = time.time() - start_time
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            knowledge_ids = self.extract_knowledge_ids(content)

            return ModelResult(
                knowledge_ids=knowledge_ids,
                response_time=response_time,
                raw_response=content
            )

        except Exception as e:
            logger.error(f"GPT-5 API call failed: {e}")
            return ModelResult(
                knowledge_ids=set(),
                response_time=time.time() - start_time,
                raw_response=f"Error: {str(e)}"
            )

    def call_model_multiple_times(self, model_func, messages: List[Dict], num_calls: int = 3) -> Tuple[Set[str], Tuple[int, int], Tuple[float, float], float, str, List[ModelResult]]:
        """Call a model multiple times and return intersection, ranges, avg time, and first call content"""
        results = []
        from concurrent.futures import ThreadPoolExecutor, as_completed
        with ThreadPoolExecutor(max_workers=num_calls) as executor:
            future_to_call = {executor.submit(model_func, messages): i for i in range(num_calls)}
            for future in as_completed(future_to_call):
                i = future_to_call[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Completed call {i+1}/{num_calls}")
                except Exception as e:
                    logger.error(f"Error in call {i+1}/{num_calls}: {e}")

        # Calculate intersection of knowledge IDs
        if results:
            intersection = results[0].knowledge_ids
            for result in results[1:]:
                intersection = intersection.intersection(result.knowledge_ids)
        else:
            intersection = set()

        # Calculate count and time ranges
        counts = [len(result.knowledge_ids) for result in results]
        times = [result.response_time for result in results]

        count_range = (min(counts) if counts else 0, max(counts) if counts else 0)
        time_range = (min(times) if times else 0.0, max(times) if times else 0.0)
        avg_time = sum(times) / len(times) if times else 0.0

        # Get first call content
        first_call_content = results[0].raw_response if results else ""

        return intersection, count_range, time_range, avg_time, first_call_content, results

    def evaluate_case(self, case_index: int, case_data: Dict) -> CaseEvaluation:
        """Evaluate a single case"""
        logger.info(f"Evaluating case {case_index + 1}")

        case_messages = case_data["msg"]
        existing_result = case_data.get("result", "")
        existing_content = case_data.get("content", "")

        # Extract case content for reporting
        case_content = ""
        for msg in case_messages:
            if msg['role'] == 'user':
                case_content = msg['content']
                break

        # 1. Establish ground truth with Claude
        if existing_result and existing_result.strip():
            # Use existing result as ground truth
            logger.info("Using existing result as ground truth...")
            ground_truth_ids = set(existing_result.split(','))
            gt_count_range = (len(ground_truth_ids), len(ground_truth_ids))

            # Use cached timing info if available, otherwise default to 0
            time_min = case_data.get("time_min", 0.0)
            time_max = case_data.get("time_max", 0.0)
            gt_time_range = (time_min, time_max)
            gt_avg_time = case_data.get("time_avg", round((time_min + time_max) / 2, 3))
            ground_truth_content = existing_content  # Use existing content
        else:
            # Generate new ground truth with Claude (3 calls)
            logger.info("Establishing ground truth with Claude...")
            ground_truth_ids, gt_count_range, gt_time_range, gt_avg_time, ground_truth_content, claude_results = self.call_model_multiple_times(
                self.call_claude, case_messages, 3
            )
            # Update the case data with the new result, content, and timing info
            case_data["result"] = ','.join(sorted(ground_truth_ids))
            case_data["content"] = ground_truth_content
            case_data["time_min"] = round(gt_time_range[0], 3)
            case_data["time_max"] = round(gt_time_range[1], 3)
            case_data["time_avg"] = round(gt_avg_time, 3)

        # 2. Evaluate Gemini with reasoning (3 calls)
        logger.info("Evaluating Gemini with reasoning...")
        gemini_reasoning_ids, gr_count_range, gr_time_range, gr_avg_time, gemini_reasoning_content, gemini_reasoning_results = self.call_model_multiple_times(
            self.call_gemini_reasoning, case_messages, 3
        )

        # 2b. Evaluate Gemini with small reasoning (3 calls)
        logger.info("Evaluating Gemini with small reasoning...")
        gemini_reasoning_small_ids, grs_count_range, grs_time_range, grs_avg_time, gemini_reasoning_small_content, gemini_reasoning_small_results = self.call_model_multiple_times(
            self.call_gemini_reasoning_small, case_messages, 3
        )

        # 3. Evaluate Gemini without reasoning (3 calls)
        logger.info("Evaluating Gemini without reasoning...")
        gemini_close_ids, gc_count_range, gc_time_range, gc_avg_time, gemini_close_content, gemini_close_results = self.call_model_multiple_times(
            self.call_gemini_close, case_messages, 3
        )

        # 4. Evaluate GPT-5 (3 calls)
        logger.info("Evaluating GPT-5...")
        gpt5_ids, gpt5_count_range, gpt5_time_range, gpt5_avg_time, gpt5_content, gpt5_results = self.call_model_multiple_times(
            self.call_gpt5, case_messages, 3
        )

        # Check validity (ground truth should be subset of model results)
        gemini_reasoning_valid = ground_truth_ids.issubset(gemini_reasoning_ids)
        gemini_reasoning_small_valid = ground_truth_ids.issubset(gemini_reasoning_small_ids)
        gemini_close_valid = ground_truth_ids.issubset(gemini_close_ids)
        gpt5_valid = ground_truth_ids.issubset(gpt5_ids)

        return CaseEvaluation(
            case_index=case_index,
            case_content=case_content,
            ground_truth_ids=ground_truth_ids,
            ground_truth_count_range=gt_count_range,
            ground_truth_time_range=gt_time_range,
            ground_truth_avg_time=gt_avg_time,
            ground_truth_content=ground_truth_content,
            gemini_reasoning_ids=gemini_reasoning_ids,
            gemini_reasoning_valid=gemini_reasoning_valid,
            gemini_reasoning_count_range=gr_count_range,
            gemini_reasoning_time_range=gr_time_range,
            gemini_reasoning_avg_time=gr_avg_time,
            gemini_reasoning_content=gemini_reasoning_content,
            gemini_reasoning_small_ids=gemini_reasoning_small_ids,
            gemini_reasoning_small_valid=gemini_reasoning_small_valid,
            gemini_reasoning_small_count_range=grs_count_range,
            gemini_reasoning_small_time_range=grs_time_range,
            gemini_reasoning_small_avg_time=grs_avg_time,
            gemini_reasoning_small_content=gemini_reasoning_small_content,
            gemini_close_ids=gemini_close_ids,
            gemini_close_valid=gemini_close_valid,
            gemini_close_count_range=gc_count_range,
            gemini_close_time_range=gc_time_range,
            gemini_close_avg_time=gc_avg_time,
            gemini_close_content=gemini_close_content,
            gpt5_ids=gpt5_ids,
            gpt5_valid=gpt5_valid,
            gpt5_count_range=gpt5_count_range,
            gpt5_time_range=gpt5_time_range,
            gpt5_avg_time=gpt5_avg_time,
            gpt5_content=gpt5_content
        )

    def evaluate_case_gemini_gt(self, case_index: int, case_data: Dict) -> CaseEvaluation:
        """Evaluate a single case with Gemini as ground truth"""
        logger.info(f"Evaluating case {case_index + 1} (Gemini GT mode)")

        case_messages = case_data["msg"]

        # Extract case content for reporting
        case_content = ""
        for msg in case_messages:
            if msg['role'] == 'user':
                case_content = msg['content']
                break

        # 1. Establish ground truth with Gemini reasoning (3 calls)
        logger.info("Establishing ground truth with Gemini reasoning...")
        ground_truth_ids, gt_count_range, gt_time_range, gt_avg_time, ground_truth_content, gemini_gt_results = self.call_model_multiple_times(
            self.call_gemini_reasoning, case_messages, 3
        )

        # 2. Evaluate Gemini with small reasoning (3 calls)
        logger.info("Evaluating Gemini with small reasoning...")
        gemini_reasoning_small_ids, grs_count_range, grs_time_range, grs_avg_time, gemini_reasoning_small_content, gemini_reasoning_small_results = self.call_model_multiple_times(
            self.call_gemini_reasoning_small, case_messages, 3
        )

        # 3. Evaluate Gemini without reasoning (3 calls)
        logger.info("Evaluating Gemini without reasoning...")
        gemini_close_ids, gc_count_range, gc_time_range, gc_avg_time, gemini_close_content, gemini_close_results = self.call_model_multiple_times(
            self.call_gemini_close, case_messages, 3
        )

        # 4. Evaluate GPT-5 (3 calls)
        logger.info("Evaluating GPT-5...")
        gpt5_ids, gpt5_count_range, gpt5_time_range, gpt5_avg_time, gpt5_content, gpt5_results = self.call_model_multiple_times(
            self.call_gpt5, case_messages, 3
        )

        # Check validity (ground truth should be subset of model results)
        gemini_reasoning_small_valid = ground_truth_ids.issubset(gemini_reasoning_small_ids)
        gemini_close_valid = ground_truth_ids.issubset(gemini_close_ids)
        gpt5_valid = ground_truth_ids.issubset(gpt5_ids)

        return CaseEvaluation(
            case_index=case_index,
            case_content=case_content,
            ground_truth_ids=ground_truth_ids,
            ground_truth_count_range=gt_count_range,
            ground_truth_time_range=gt_time_range,
            ground_truth_avg_time=gt_avg_time,
            ground_truth_content=ground_truth_content,
            gemini_reasoning_ids=ground_truth_ids,  # Same as GT
            gemini_reasoning_valid=True,  # Always true since GT = reasoning
            gemini_reasoning_count_range=gt_count_range,
            gemini_reasoning_time_range=gt_time_range,
            gemini_reasoning_avg_time=gt_avg_time,
            gemini_reasoning_content=ground_truth_content,
            gemini_reasoning_small_ids=gemini_reasoning_small_ids,
            gemini_reasoning_small_valid=gemini_reasoning_small_valid,
            gemini_reasoning_small_count_range=grs_count_range,
            gemini_reasoning_small_time_range=grs_time_range,
            gemini_reasoning_small_avg_time=grs_avg_time,
            gemini_reasoning_small_content=gemini_reasoning_small_content,
            gemini_close_ids=gemini_close_ids,
            gemini_close_valid=gemini_close_valid,
            gemini_close_count_range=gc_count_range,
            gemini_close_time_range=gc_time_range,
            gemini_close_avg_time=gc_avg_time,
            gemini_close_content=gemini_close_content,
            gpt5_ids=gpt5_ids,
            gpt5_valid=gpt5_valid,
            gpt5_count_range=gpt5_count_range,
            gpt5_time_range=gpt5_time_range,
            gpt5_avg_time=gpt5_avg_time,
            gpt5_content=gpt5_content
        )

    def save_results_to_csv(self, evaluations: List[CaseEvaluation], filename: str = "evaluation_results.csv"):
        """Save evaluation results to CSV file"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'case_index',
                'case_content',
                'ground_truth_ids',
                'ground_truth_count_min',
                'ground_truth_count_max',
                'ground_truth_time_min',
                'ground_truth_time_max',
                'ground_truth_time_avg',
                'ground_truth_content',
                'gemini_reasoning_ids',
                'gemini_reasoning_valid',
                'gemini_reasoning_count_min',
                'gemini_reasoning_count_max',
                'gemini_reasoning_time_min',
                'gemini_reasoning_time_max',
                'gemini_reasoning_time_avg',
                'gemini_reasoning_content',
                # New: Gemini reasoning small columns
                'gemini_reasoning_small_ids',
                'gemini_reasoning_small_valid',
                'gemini_reasoning_small_count_min',
                'gemini_reasoning_small_count_max',
                'gemini_reasoning_small_time_min',
                'gemini_reasoning_small_time_max',
                'gemini_reasoning_small_time_avg',
                'gemini_reasoning_small_content',
                'gemini_close_ids',
                'gemini_close_valid',
                'gemini_close_count_min',
                'gemini_close_count_max',
                'gemini_close_time_min',
                'gemini_close_time_max',
                'gemini_close_time_avg',
                'gemini_close_content',
                # New: GPT-5 columns
                'gpt5_ids',
                'gpt5_valid',
                'gpt5_count_min',
                'gpt5_count_max',
                'gpt5_time_min',
                'gpt5_time_max',
                'gpt5_time_avg',
                'gpt5_content'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for eval_result in evaluations:
                writer.writerow({
                    'case_index': eval_result.case_index + 1,
                    'case_content': eval_result.case_content,
                    'ground_truth_ids': ';'.join(sorted(eval_result.ground_truth_ids)),
                    'ground_truth_count_min': eval_result.ground_truth_count_range[0],
                    'ground_truth_count_max': eval_result.ground_truth_count_range[1],
                    'ground_truth_time_min': round(eval_result.ground_truth_time_range[0], 3),
                    'ground_truth_time_max': round(eval_result.ground_truth_time_range[1], 3),
                    'ground_truth_time_avg': round(eval_result.ground_truth_avg_time, 3),
                    'ground_truth_content': eval_result.ground_truth_content,
                    'gemini_reasoning_ids': ';'.join(sorted(eval_result.gemini_reasoning_ids)),
                    'gemini_reasoning_valid': eval_result.gemini_reasoning_valid,
                    'gemini_reasoning_count_min': eval_result.gemini_reasoning_count_range[0],
                    'gemini_reasoning_count_max': eval_result.gemini_reasoning_count_range[1],
                    'gemini_reasoning_time_min': round(eval_result.gemini_reasoning_time_range[0], 3),
                    'gemini_reasoning_time_max': round(eval_result.gemini_reasoning_time_range[1], 3),
                    'gemini_reasoning_time_avg': round(eval_result.gemini_reasoning_avg_time, 3),
                    'gemini_reasoning_content': eval_result.gemini_reasoning_content,
                    # New: Gemini reasoning small
                    'gemini_reasoning_small_ids': ';'.join(sorted(eval_result.gemini_reasoning_small_ids)),
                    'gemini_reasoning_small_valid': eval_result.gemini_reasoning_small_valid,
                    'gemini_reasoning_small_count_min': eval_result.gemini_reasoning_small_count_range[0],
                    'gemini_reasoning_small_count_max': eval_result.gemini_reasoning_small_count_range[1],
                    'gemini_reasoning_small_time_min': round(eval_result.gemini_reasoning_small_time_range[0], 3),
                    'gemini_reasoning_small_time_max': round(eval_result.gemini_reasoning_small_time_range[1], 3),
                    'gemini_reasoning_small_time_avg': round(eval_result.gemini_reasoning_small_avg_time, 3),
                    'gemini_reasoning_small_content': eval_result.gemini_reasoning_small_content,
                    'gemini_close_ids': ';'.join(sorted(eval_result.gemini_close_ids)),
                    'gemini_close_valid': eval_result.gemini_close_valid,
                    'gemini_close_count_min': eval_result.gemini_close_count_range[0],
                    'gemini_close_count_max': eval_result.gemini_close_count_range[1],
                    'gemini_close_time_min': round(eval_result.gemini_close_time_range[0], 3),
                    'gemini_close_time_max': round(eval_result.gemini_close_time_range[1], 3),
                    'gemini_close_time_avg': round(eval_result.gemini_close_avg_time, 3),
                    'gemini_close_content': eval_result.gemini_close_content,
                    # New: GPT-5 data
                    'gpt5_ids': ';'.join(sorted(eval_result.gpt5_ids)),
                    'gpt5_valid': eval_result.gpt5_valid,
                    'gpt5_count_min': eval_result.gpt5_count_range[0],
                    'gpt5_count_max': eval_result.gpt5_count_range[1],
                    'gpt5_time_min': round(eval_result.gpt5_time_range[0], 3),
                    'gpt5_time_max': round(eval_result.gpt5_time_range[1], 3),
                    'gpt5_time_avg': round(eval_result.gpt5_avg_time, 3),
                    'gpt5_content': eval_result.gpt5_content
                })

        logger.info(f"Results saved to {filename}")

    def save_updated_data(self, cases: List[Dict], data_file: str):
        """Save updated data back to file with new results"""
        try:
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(cases, f, ensure_ascii=False, indent=2)
            logger.info(f"Updated data saved back to {data_file}")
        except Exception as e:
            logger.error(f"Error saving updated data: {e}")

    def run_evaluation(self, data_file: str = "data_small_claude_gt.json", mode: str = "claude"):
        """Run evaluation with specified ground truth mode"""
        if mode == "gemini":
            return self.run_evaluation_gemini_gt(data_file)
        else:
            # Original Claude GT mode
            logger.info(f"Starting evaluation with Claude GT from {data_file}")

            # Load data
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    cases = json.load(f)
            except FileNotFoundError:
                logger.error(f"Data file {data_file} not found")
                return
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON file: {e}")
                return

            logger.info(f"Loaded {len(cases)} cases from {data_file}")

            evaluations = []

            # Process each case
            for i, case_data in enumerate(cases):
                try:
                    evaluation = self.evaluate_case(i, case_data)
                    evaluations.append(evaluation)

                    # Log progress
                    logger.info(f"Case {i+1}/{len(cases)} completed")
                    logger.info(f"Ground truth: {len(evaluation.ground_truth_ids)} IDs")
                    logger.info(f"Gemini reasoning valid: {evaluation.gemini_reasoning_valid}")
                    logger.info(f"Gemini reasoning small valid: {evaluation.gemini_reasoning_small_valid}")
                    logger.info(f"Gemini close valid: {evaluation.gemini_close_valid}")
                    logger.info(f"GPT-5 valid: {evaluation.gpt5_valid}")
                    logger.info("-" * 50)

                except Exception as e:
                    logger.error(f"Error evaluating case {i+1}: {e}")
                    continue

            # Save updated data back to file (with new results)
            self.save_updated_data(cases, data_file)

            # Save results
            csv_file = data_file.replace('.json', '.csv')
            print(f"xxxxxxxSaving results to {csv_file}")
            self.save_results_to_csv(evaluations, csv_file) # Save results

            # Print summary
            summary_file = data_file.replace('.json', '_summary.txt')
            self.print_summary(evaluations, summary_file)

    def print_summary(self, evaluations: List[CaseEvaluation], output_file: str = None):
        """Print evaluation summary"""
        total_cases = len(evaluations)
        reasoning_valid = sum(1 for e in evaluations if e.gemini_reasoning_valid)
        reasoning_small_valid = sum(1 for e in evaluations if e.gemini_reasoning_small_valid)
        close_valid = sum(1 for e in evaluations if e.gemini_close_valid)
        gpt5_valid = sum(1 for e in evaluations if e.gpt5_valid)

        # Prepare output lines
        lines = []
        lines.append("=" * 60)
        lines.append("EVALUATION SUMMARY")
        lines.append("=" * 60)
        lines.append(f"Total cases evaluated: {total_cases}")
        lines.append(f"Gemini reasoning valid cases: {reasoning_valid}/{total_cases} ({reasoning_valid/total_cases*100:.1f}%)")
        lines.append(f"Gemini reasoning small valid cases: {reasoning_small_valid}/{total_cases} ({reasoning_small_valid/total_cases*100:.1f}%)")
        lines.append(f"Gemini close valid cases: {close_valid}/{total_cases} ({close_valid/total_cases*100:.1f}%)")
        lines.append(f"GPT-5 valid cases: {gpt5_valid}/{total_cases} ({gpt5_valid/total_cases*100:.1f}%)")

        if evaluations:
            # Calculate overall averages
            avg_gt_count = sum(len(e.ground_truth_ids) for e in evaluations) / len(evaluations)
            avg_reasoning_count = sum(len(e.gemini_reasoning_ids) for e in evaluations) / len(evaluations)
            avg_reasoning_small_count = sum(len(e.gemini_reasoning_small_ids) for e in evaluations) / len(evaluations)
            avg_close_count = sum(len(e.gemini_close_ids) for e in evaluations) / len(evaluations)
            avg_gpt5_count = sum(len(e.gpt5_ids) for e in evaluations) / len(evaluations)

            # Calculate average times using *_avg_time
            avg_gt_time = sum(e.ground_truth_avg_time for e in evaluations) / len(evaluations)
            avg_reasoning_time = sum(e.gemini_reasoning_avg_time for e in evaluations) / len(evaluations)
            avg_reasoning_small_time = sum(e.gemini_reasoning_small_avg_time for e in evaluations) / len(evaluations)
            avg_close_time = sum(e.gemini_close_avg_time for e in evaluations) / len(evaluations)
            avg_gpt5_time = sum(e.gpt5_avg_time for e in evaluations) / len(evaluations)

            lines.append(f"Average ground truth knowledge count: {avg_gt_count:.1f}")
            lines.append(f"Average Gemini reasoning knowledge count: {avg_reasoning_count:.1f}")
            lines.append(f"Average Gemini reasoning small knowledge count: {avg_reasoning_small_count:.1f}")
            lines.append(f"Average Gemini close knowledge count: {avg_close_count:.1f}")
            lines.append(f"Average GPT-5 knowledge count: {avg_gpt5_count:.1f}")
            lines.append("")
            lines.append(f"Average ground truth response time: {avg_gt_time:.3f}s")
            lines.append(f"Average Gemini reasoning response time: {avg_reasoning_time:.3f}s")
            lines.append(f"Average Gemini reasoning small response time: {avg_reasoning_small_time:.3f}s")
            lines.append(f"Average Gemini close response time: {avg_close_time:.3f}s")
            lines.append(f"Average GPT-5 response time: {avg_gpt5_time:.3f}s")
            lines.append("")

            # Detailed case-by-case breakdown
            lines.append("DETAILED CASE BREAKDOWN:")
            lines.append("-" * 40)
            for eval_result in evaluations:
                gt_avg_time = eval_result.ground_truth_avg_time
                reasoning_avg_time = eval_result.gemini_reasoning_avg_time
                reasoning_small_avg_time = eval_result.gemini_reasoning_small_avg_time
                close_avg_time = eval_result.gemini_close_avg_time
                gpt5_avg_time = eval_result.gpt5_avg_time

                lines.append(f"Case {eval_result.case_index + 1}:")
                lines.append(f"  Ground Truth: {len(eval_result.ground_truth_ids)} IDs, "
                           f"Time: [{eval_result.ground_truth_time_range[0]:.3f}-{eval_result.ground_truth_time_range[1]:.3f}]s "
                           f"(avg: {gt_avg_time:.3f}s)")
                lines.append(f"  Reasoning: {len(eval_result.gemini_reasoning_ids)} IDs, "
                           f"Valid: {eval_result.gemini_reasoning_valid}, "
                           f"Time: [{eval_result.gemini_reasoning_time_range[0]:.3f}-{eval_result.gemini_reasoning_time_range[1]:.3f}]s "
                           f"(avg: {reasoning_avg_time:.3f}s)")
                lines.append(f"  Reasoning Small: {len(eval_result.gemini_reasoning_small_ids)} IDs, "
                           f"Valid: {eval_result.gemini_reasoning_small_valid}, "
                           f"Time: [{eval_result.gemini_reasoning_small_time_range[0]:.3f}-{eval_result.gemini_reasoning_small_time_range[1]:.3f}]s "
                           f"(avg: {reasoning_small_avg_time:.3f}s)")
                lines.append(f"  Close: {len(eval_result.gemini_close_ids)} IDs, "
                           f"Valid: {eval_result.gemini_close_valid}, "
                           f"Time: [{eval_result.gemini_close_time_range[0]:.3f}-{eval_result.gemini_close_time_range[1]:.3f}]s "
                           f"(avg: {close_avg_time:.3f}s)")
                lines.append(f"  GPT-5: {len(eval_result.gpt5_ids)} IDs, "
                           f"Valid: {eval_result.gpt5_valid}, "
                           f"Time: [{eval_result.gpt5_time_range[0]:.3f}-{eval_result.gpt5_time_range[1]:.3f}]s "
                           f"(avg: {gpt5_avg_time:.3f}s)")
                lines.append("")

        # Output to console
        for line in lines:
            logger.info(line)

        # Output to file if specified
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    for line in lines:
                        f.write(line + '\n')
                logger.info(f"Summary saved to {output_file}")
            except Exception as e:
                logger.error(f"Error saving summary to file: {e}")

    def run_evaluation_gemini_gt(self, data_file: str = "data_small_gemini_gt.json"):
        """Run evaluation with Gemini as ground truth"""
        logger.info(f"Starting evaluation with Gemini GT from {data_file}")

        # Load test cases
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                cases = json.load(f)
        except FileNotFoundError:
            logger.error(f"Data file {data_file} not found")
            return
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON file: {e}")
            return

        logger.info(f"Loaded {len(cases)} cases from {data_file}")

        evaluations = []

        # Process each case
        for i, case_data in enumerate(cases):
            try:
                evaluation = self.evaluate_case_gemini_gt(i, case_data)
                evaluations.append(evaluation)

                # Log progress
                logger.info(f"Case {i+1}/{len(cases)} completed")
                logger.info(f"Ground truth (Gemini): {len(evaluation.ground_truth_ids)} IDs")
                logger.info(f"Gemini small valid: {evaluation.gemini_reasoning_small_valid}")
                logger.info(f"Gemini close valid: {evaluation.gemini_close_valid}")
                logger.info(f"GPT-5 valid: {evaluation.gpt5_valid}")
                logger.info("-" * 50)

            except Exception as e:
                logger.error(f"Error evaluating case {i+1}: {e}")
                continue

        # Save results
        csv_file = data_file.replace('.json', '.csv')
        self.save_results_to_csv(evaluations, csv_file)

        summary_file = data_file.replace('.json', '_summary.txt')
        self.print_summary_gemini_gt(evaluations, summary_file)

    def print_summary_gemini_gt(self, evaluations: List[CaseEvaluation], output_file: str = None):
        """Print evaluation summary for Gemini GT mode"""
        total_cases = len(evaluations)
        reasoning_small_valid = sum(1 for e in evaluations if e.gemini_reasoning_small_valid)
        close_valid = sum(1 for e in evaluations if e.gemini_close_valid)
        gpt5_valid = sum(1 for e in evaluations if e.gpt5_valid)

        # Prepare output lines
        lines = []
        lines.append("=" * 80)
        lines.append("EVALUATION SUMMARY (Gemini GT Mode)")
        lines.append("=" * 80)
        lines.append(f"Total cases evaluated: {total_cases}")
        lines.append(f"Gemini reasoning small valid cases: {reasoning_small_valid}/{total_cases} ({reasoning_small_valid/total_cases*100:.1f}%)")
        lines.append(f"Gemini close valid cases: {close_valid}/{total_cases} ({close_valid/total_cases*100:.1f}%)")
        lines.append(f"GPT-5 valid cases: {gpt5_valid}/{total_cases} ({gpt5_valid/total_cases*100:.1f}%)")

        if evaluations:
            # Calculate overall averages
            avg_gt_count = sum(len(e.ground_truth_ids) for e in evaluations) / len(evaluations)
            avg_reasoning_small_count = sum(len(e.gemini_reasoning_small_ids) for e in evaluations) / len(evaluations)
            avg_close_count = sum(len(e.gemini_close_ids) for e in evaluations) / len(evaluations)
            avg_gpt5_count = sum(len(e.gpt5_ids) for e in evaluations) / len(evaluations)

            # Calculate average times using *_avg_time
            avg_gt_time = sum(e.ground_truth_avg_time for e in evaluations) / len(evaluations)
            avg_reasoning_small_time = sum(e.gemini_reasoning_small_avg_time for e in evaluations) / len(evaluations)
            avg_close_time = sum(e.gemini_close_avg_time for e in evaluations) / len(evaluations)
            avg_gpt5_time = sum(e.gpt5_avg_time for e in evaluations) / len(evaluations)

            lines.append(f"Average ground truth (Gemini) knowledge count: {avg_gt_count:.1f}")
            lines.append(f"Average Gemini reasoning small knowledge count: {avg_reasoning_small_count:.1f}")
            lines.append(f"Average Gemini close knowledge count: {avg_close_count:.1f}")
            lines.append(f"Average GPT-5 knowledge count: {avg_gpt5_count:.1f}")
            lines.append("")
            lines.append(f"Average ground truth (Gemini) response time: {avg_gt_time:.3f}s")
            lines.append(f"Average Gemini reasoning small response time: {avg_reasoning_small_time:.3f}s")
            lines.append(f"Average Gemini close response time: {avg_close_time:.3f}s")
            lines.append(f"Average GPT-5 response time: {avg_gpt5_time:.3f}s")
            lines.append("")

            # Detailed case-by-case breakdown
            lines.append("DETAILED CASE BREAKDOWN:")
            lines.append("-" * 40)
            for eval_result in evaluations:
                gt_avg_time = eval_result.ground_truth_avg_time
                reasoning_small_avg_time = eval_result.gemini_reasoning_small_avg_time
                close_avg_time = eval_result.gemini_close_avg_time
                gpt5_avg_time = eval_result.gpt5_avg_time

                lines.append(f"Case {eval_result.case_index + 1}:")
                lines.append(f"  Ground Truth (Gemini): {len(eval_result.ground_truth_ids)} IDs, "
                           f"Time: [{eval_result.ground_truth_time_range[0]:.3f}-{eval_result.ground_truth_time_range[1]:.3f}]s "
                           f"(avg: {gt_avg_time:.3f}s)")
                lines.append(f"  Reasoning Small: {len(eval_result.gemini_reasoning_small_ids)} IDs, "
                           f"Valid: {eval_result.gemini_reasoning_small_valid}, "
                           f"Time: [{eval_result.gemini_reasoning_small_time_range[0]:.3f}-{eval_result.gemini_reasoning_small_time_range[1]:.3f}]s "
                           f"(avg: {reasoning_small_avg_time:.3f}s)")
                lines.append(f"  Close: {len(eval_result.gemini_close_ids)} IDs, "
                           f"Valid: {eval_result.gemini_close_valid}, "
                           f"Time: [{eval_result.gemini_close_time_range[0]:.3f}-{eval_result.gemini_close_time_range[1]:.3f}]s "
                           f"(avg: {close_avg_time:.3f}s)")
                lines.append(f"  GPT-5: {len(eval_result.gpt5_ids)} IDs, "
                           f"Valid: {eval_result.gpt5_valid}, "
                           f"Time: [{eval_result.gpt5_time_range[0]:.3f}-{eval_result.gpt5_time_range[1]:.3f}]s "
                           f"(avg: {gpt5_avg_time:.3f}s)")
                lines.append("")

        # Output to console
        for line in lines:
            logger.info(line)

        # Output to file if specified
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    for line in lines:
                        f.write(line + '\n')
                logger.info(f"Summary saved to {output_file}")
            except Exception as e:
                logger.error(f"Error saving summary to file: {e}")

def main():
    """Main function"""
    evaluator = ModelEvaluator()
    evaluator.run_evaluation()
    # evaluator.run_evaluation_gemini_gt()

if __name__ == "__main__":
    main()
