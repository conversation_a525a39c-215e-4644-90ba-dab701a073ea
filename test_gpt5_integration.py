#!/usr/bin/env python3
"""
Test script to verify GPT-5 integration in model evaluation
"""

import json
from model_evaluation import ModelEvaluator

def test_gpt5_integration():
    """Test GPT-5 integration with a single case"""
    print("Testing GPT-5 integration...")
    
    # Create evaluator instance
    evaluator = ModelEvaluator()
    
    # Create a simple test case
    test_case = {
        "msg": [
            {
                "content": "Carefully analyze each knowledge item in relation to the user's task, and select ALL relevant and useful knowledge items that could assist with any aspect of the task. Consider both explicit and implicit requirements of the task. If no knowledge items are useful, just return \"no useful knowledge items\".\nNote: You have only one chance to choose knowledge items, make sure to choose all you need. You can select multiple knowledge items all at once.\n\n# Available knowledge items you can choose from:\n\n<item id=\"system.planner_11\">\n<title>diagrams</title>\n<use_this_knowledge_when>\nUser's task may require drawing diagrams\n</use_this_knowledge_when>\n</item>\n\nOutput format:\n<rationale>short rationale(one sentence) why to choose knowledge id 1</rationale>\n<id>1</id>",
                "role": "system"
            },
            {
                "content": "User's task:\n<task>\nCreate a simple flowchart showing the process of making coffee.\n</task>\n\nWalk through knowledge items one by one, examining if the knowledge is possibly relevant to the task.",
                "role": "user"
            }
        ]
    }
    
    # Test GPT-5 call
    print("Testing GPT-5 API call...")
    try:
        result = evaluator.call_gpt5(test_case["msg"])
        print(f"GPT-5 Response Time: {result.response_time:.3f}s")
        print(f"GPT-5 Knowledge IDs: {result.knowledge_ids}")
        print(f"GPT-5 Response Preview: {result.raw_response[:200]}...")
        print("✅ GPT-5 integration test passed!")
        return True
    except Exception as e:
        print(f"❌ GPT-5 integration test failed: {e}")
        return False

def test_evaluation_with_gpt5():
    """Test full evaluation including GPT-5"""
    print("\nTesting full evaluation with GPT-5...")
    
    # Load a small subset of test data
    try:
        with open('data_small_claude_gt.json', 'r', encoding='utf-8') as f:
            cases = json.load(f)
    except FileNotFoundError:
        print("❌ Test data file not found")
        return False
    
    # Take only the first case for testing
    test_cases = cases[:1]
    
    # Create evaluator and test
    evaluator = ModelEvaluator()
    
    try:
        evaluation = evaluator.evaluate_case(0, test_cases[0])
        
        print(f"✅ Evaluation completed successfully!")
        print(f"Ground Truth IDs: {len(evaluation.ground_truth_ids)}")
        print(f"Gemini Reasoning Valid: {evaluation.gemini_reasoning_valid}")
        print(f"Gemini Reasoning Small Valid: {evaluation.gemini_reasoning_small_valid}")
        print(f"Gemini Close Valid: {evaluation.gemini_close_valid}")
        print(f"GPT-5 Valid: {evaluation.gpt5_valid}")
        print(f"GPT-5 IDs Count: {len(evaluation.gpt5_ids)}")
        print(f"GPT-5 Avg Time: {evaluation.gpt5_avg_time:.3f}s")
        
        return True
    except Exception as e:
        print(f"❌ Full evaluation test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("GPT-5 INTEGRATION TEST")
    print("=" * 60)
    
    # Test 1: Basic GPT-5 API call
    test1_passed = test_gpt5_integration()
    
    # Test 2: Full evaluation with GPT-5
    test2_passed = test_evaluation_with_gpt5()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"GPT-5 API Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Full Evaluation Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! GPT-5 integration is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
