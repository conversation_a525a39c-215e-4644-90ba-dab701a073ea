#!/usr/bin/env python3
"""
CSV to JSON converter for prompt data
Converts CSV with prompt column to JSON format with system/user messages
"""

import csv
import json
import re
from typing import List, Dict

def extract_system_user_content(prompt: str) -> tuple:
    """Extract system and user content from prompt string"""
    
    # Find system content between <system> tags
    system_match = re.search(r'<system>(.*?)</system>', prompt, re.DOTALL)
    system_content = system_match.group(1).strip() if system_match else ""
    
    # Find user content between <user> tags
    user_match = re.search(r'<user>(.*?)</user>', prompt, re.DOTALL)
    user_content = user_match.group(1).strip() if user_match else ""
    
    return system_content, user_content

def convert_csv_to_json(csv_file: str = "knowledge_long_time_data.csv", 
                       output_file: str = "converted_prompts.json"):
    """Convert CSV with prompt column to JSON format"""
    
    result_data = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row_num, row in enumerate(reader, 1):
                prompt = row.get('prompt', '')
                
                if not prompt:
                    print(f"Warning: Empty prompt in row {row_num}, skipping...")
                    continue
                
                # Extract system and user content
                system_content, user_content = extract_system_user_content(prompt)
                
                if not system_content and not user_content:
                    print(f"Warning: No system/user tags found in row {row_num}, skipping...")
                    continue
                
                # Create JSON structure
                case_data = {
                    "msg": [
                        {
                            "content": system_content,
                            "role": "system"
                        },
                        {
                            "content": user_content,
                            "role": "user"
                        }
                    ]
                }
                
                result_data.append(case_data)
                print(f"Processed row {row_num}: {len(system_content)} chars (system), {len(user_content)} chars (user)")
        
        # Save to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ Conversion completed!")
        print(f"📊 Total cases processed: {len(result_data)}")
        print(f"📁 Output saved to: {output_file}")
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{csv_file}' not found")
    except Exception as e:
        print(f"❌ Error during conversion: {e}")

def main():
    """Main function with command line support"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert CSV prompts to JSON format')
    parser.add_argument('--input', default='knowledge_long_time_data.csv',
                       help='Input CSV file (default: knowledge_long_time_data.csv)')
    parser.add_argument('--output', default='converted_prompts.json',
                       help='Output JSON file (default: converted_prompts.json)')
    
    args = parser.parse_args()
    
    convert_csv_to_json(args.input, args.output)

if __name__ == "__main__":
    main()