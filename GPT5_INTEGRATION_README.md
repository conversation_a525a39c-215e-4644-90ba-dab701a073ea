# GPT-5 Integration for Model Evaluation

## Overview

GPT-5 evaluation has been successfully integrated into the model evaluation script. The system now evaluates 4 different models against the ground truth:

1. **Claude 3.7 Sonnet** (Ground Truth)
2. **Gemini-2.5 Flash with Reasoning** (2048 tokens)
3. **Gemini-2.5 Flash with Small Reasoning** (512 tokens)  
4. **Gemini-2.5 Flash without Reasoning**
5. **GPT-5** (NEW)

## What's New

### GPT-5 API Integration
- **Model**: `gpt-5-2025-08-07`
- **Endpoint**: `https://search.bytedance.net/gpt/openapi/online/v2/crawl`
- **API Key**: `GDlcrsO2neW02VNGGVV5Bk5TUWgtABeP`
- **Max Tokens**: 2048
- **Temperature**: 1.0
- **Message Format**: Content array format (similar to Claude)

### Enhanced Data Structure
The `CaseEvaluation` dataclass now includes GPT-5 fields:
- `gpt5_ids`: Set of knowledge IDs extracted from GPT-5 responses
- `gpt5_valid`: Boolean indicating if ground truth is subset of GPT-5 results
- `gpt5_count_range`: Tuple of (min, max) knowledge ID counts across 3 calls
- `gpt5_time_range`: Tuple of (min, max) response times across 3 calls
- `gpt5_avg_time`: Average response time across 3 calls
- `gpt5_content`: Raw response content from first GPT-5 call

### Enhanced CSV Output
The CSV output now includes additional columns for GPT-5:
- `gpt5_ids`: Semicolon-separated list of knowledge IDs
- `gpt5_valid`: Validation status
- `gpt5_count_min/max`: Knowledge ID count range
- `gpt5_time_min/max/avg`: Response time statistics
- `gpt5_content`: Raw response content

### Enhanced Summary Reports
Both summary modes now include GPT-5 statistics:
- Valid case percentages
- Average knowledge ID counts
- Average response times
- Detailed case-by-case breakdowns

## Usage

### Running Evaluation with GPT-5

```bash
# Run evaluation with Claude as ground truth (includes GPT-5)
python3 model_evaluation.py

# Or use the run script
python3 run_evaluation.py
```

### Testing GPT-5 Integration

```bash
# Run the integration test
python3 test_gpt5_integration.py
```

### Expected Output Files

When running evaluation, you'll get:
- `data_small_claude_gt.csv` - Results with GPT-5 columns
- `data_small_claude_gt_summary.txt` - Summary including GPT-5 stats
- Updated JSON file with cached results

## Performance Characteristics

Based on initial testing:
- **GPT-5 Response Time**: ~11-17 seconds per call
- **GPT-5 Knowledge ID Count**: Typically higher than other models (5 vs 1 in test case)
- **GPT-5 Validation**: Successfully identifies ground truth knowledge items

## API Configuration

The GPT-5 API uses the following configuration:

```python
payload = {
    "stream": False,
    "model": "gpt-5-2025-08-07",
    "max_tokens": 2048,
    "messages": gpt5_messages,  # Content array format
    "temperature": 1
}
```

## Error Handling

The integration includes robust error handling:
- API timeouts (60 seconds)
- Network errors
- Response parsing errors
- Graceful degradation with error messages in results

## Validation Logic

GPT-5 results are validated using the same logic as other models:
- Ground truth knowledge IDs must be a subset of GPT-5 identified IDs
- Results are aggregated across 3 parallel API calls
- Intersection of all 3 calls determines final knowledge ID set

## Integration Points

GPT-5 evaluation is integrated into:
1. `evaluate_case()` - Main evaluation method
2. `evaluate_case_gemini_gt()` - Gemini ground truth mode
3. `save_results_to_csv()` - CSV export
4. `print_summary()` - Summary generation
5. `print_summary_gemini_gt()` - Gemini GT summary

## Next Steps

The GPT-5 integration is complete and ready for production use. You can now:

1. Run full evaluations including GPT-5 performance
2. Compare GPT-5 against other models
3. Analyze GPT-5's knowledge selection patterns
4. Generate comprehensive reports with all 5 models

## Troubleshooting

If you encounter issues:

1. **API Errors**: Check network connectivity and API key validity
2. **Timeout Issues**: GPT-5 responses can be slower; timeouts are set to 60s
3. **Memory Issues**: GPT-5 responses may be longer; monitor memory usage
4. **Rate Limiting**: Consider adding delays between calls if needed

The integration maintains backward compatibility with existing evaluation workflows while adding comprehensive GPT-5 analysis capabilities.
